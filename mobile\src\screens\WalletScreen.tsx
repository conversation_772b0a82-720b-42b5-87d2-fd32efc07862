import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors } from '../utils/Colors';
import { ApiService } from '../services/ApiService';

interface WalletData {
  coins: number;
  total_earned: number;
  total_spent: number;
  phone: string;
}

interface Transaction {
  id: string;
  amount: number;
  reason: string;
  type: 'received' | 'sent';
  date: Date;
  otherParty: string;
}

const WalletScreen: React.FC = () => {
  const [walletData, setWalletData] = useState<WalletData>({
    coins: 100,
    total_earned: 100,
    total_spent: 0,
    phone: '+221771234567',
  });
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadWalletData();
  }, []);

  const loadWalletData = async () => {
    try {
      // Charger les données du portefeuille
      const wallet = await ApiService.getWallet();
      if (wallet) {
        setWalletData(wallet);
      }

      // Charger les transactions récentes
      const recentTransactions = await ApiService.getTransactions();
      if (recentTransactions) {
        setTransactions(recentTransactions);
      }
    } catch (error) {
      console.error('Erreur chargement portefeuille:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadWalletData();
    setRefreshing(false);
  };

  const handleSendCoins = () => {
    Alert.alert(
      'Envoyer des NoweeCoins',
      'Fonctionnalité de transfert en développement',
    );
  };

  const handleReceiveCoins = () => {
    Alert.alert(
      'Recevoir des NoweeCoins',
      'Partagez votre numéro de téléphone pour recevoir des coins',
    );
  };

  const handleEarnCoins = () => {
    Alert.alert(
      'Gagner des NoweeCoins',
      'Aidez quelqu\'un pour gagner des coins !\n\n• Aider quelqu\'un: +10 coins\n• Partager une ressource: +5 coins\n• Parrainer un ami: +20 coins',
    );
  };

  const renderTransaction = (transaction: Transaction) => (
    <View key={transaction.id} style={styles.transactionItem}>
      <View style={styles.transactionIcon}>
        <Icon
          name={transaction.type === 'received' ? 'arrow-downward' : 'arrow-upward'}
          size={24}
          color={transaction.type === 'received' ? Colors.success : Colors.error}
        />
      </View>
      
      <View style={styles.transactionDetails}>
        <Text style={styles.transactionReason}>{transaction.reason}</Text>
        <Text style={styles.transactionParty}>
          {transaction.type === 'received' ? 'De: ' : 'À: '}
          {transaction.otherParty}
        </Text>
        <Text style={styles.transactionDate}>
          {transaction.date.toLocaleDateString('fr-FR')}
        </Text>
      </View>
      
      <View style={styles.transactionAmount}>
        <Text
          style={[
            styles.transactionAmountText,
            {
              color: transaction.type === 'received' ? Colors.success : Colors.error,
            },
          ]}
        >
          {transaction.type === 'received' ? '+' : '-'}{transaction.amount}
        </Text>
        <Text style={styles.coinsLabel}>coins</Text>
      </View>
    </View>
  );

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Carte du portefeuille */}
      <View style={styles.walletCard}>
        <View style={styles.walletHeader}>
          <Icon name="account-balance-wallet" size={32} color={Colors.background} />
          <Text style={styles.walletTitle}>Portefeuille Nowee</Text>
        </View>
        
        <View style={styles.balanceContainer}>
          <Text style={styles.balanceAmount}>{walletData.coins}</Text>
          <Text style={styles.balanceLabel}>NoweeCoins</Text>
        </View>
        
        <View style={styles.walletStats}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{walletData.total_earned}</Text>
            <Text style={styles.statLabel}>Gagnés</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{walletData.total_spent}</Text>
            <Text style={styles.statLabel}>Dépensés</Text>
          </View>
        </View>
      </View>

      {/* Actions rapides */}
      <View style={styles.actionsContainer}>
        <TouchableOpacity style={styles.actionButton} onPress={handleSendCoins}>
          <Icon name="send" size={24} color={Colors.primary} />
          <Text style={styles.actionText}>Envoyer</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleReceiveCoins}>
          <Icon name="call-received" size={24} color={Colors.success} />
          <Text style={styles.actionText}>Recevoir</Text>
        </TouchableOpacity>
        
        <TouchableOpacity style={styles.actionButton} onPress={handleEarnCoins}>
          <Icon name="star" size={24} color={Colors.accent} />
          <Text style={styles.actionText}>Gagner</Text>
        </TouchableOpacity>
      </View>

      {/* Historique des transactions */}
      <View style={styles.transactionsContainer}>
        <Text style={styles.sectionTitle}>Transactions Récentes</Text>
        
        {transactions.length > 0 ? (
          transactions.map(renderTransaction)
        ) : (
          <View style={styles.emptyState}>
            <Icon name="history" size={48} color={Colors.textSecondary} />
            <Text style={styles.emptyStateTitle}>Aucune transaction</Text>
            <Text style={styles.emptyStateText}>
              Vos transactions apparaîtront ici
            </Text>
          </View>
        )}
      </View>

      {/* Informations sur les NoweeCoins */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>💡 À propos des NoweeCoins</Text>
        <Text style={styles.infoText}>
          Les NoweeCoins sont la monnaie locale de l'entraide. Gagnez-en en aidant votre communauté et utilisez-les pour obtenir de l'aide en retour.
        </Text>
        
        <View style={styles.infoList}>
          <Text style={styles.infoItem}>• 1 NoweeCoins = 1 minute d'aide</Text>
          <Text style={styles.infoItem}>• Transferts gratuits et instantanés</Text>
          <Text style={styles.infoItem}>• Système de réputation intégré</Text>
          <Text style={styles.infoItem}>• Économie locale circulaire</Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  walletCard: {
    margin: 16,
    padding: 24,
    borderRadius: 16,
    backgroundColor: Colors.primary,
  },
  walletHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  walletTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.background,
    marginLeft: 12,
  },
  balanceContainer: {
    alignItems: 'center',
    marginBottom: 20,
  },
  balanceAmount: {
    fontSize: 48,
    fontWeight: 'bold',
    color: Colors.background,
  },
  balanceLabel: {
    fontSize: 16,
    color: Colors.background,
    opacity: 0.9,
  },
  walletStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.background,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.background,
    opacity: 0.8,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    margin: 16,
  },
  actionButton: {
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.surface,
    borderRadius: 12,
    minWidth: 80,
  },
  actionText: {
    fontSize: 14,
    color: Colors.text,
    marginTop: 8,
    fontWeight: '500',
  },
  transactionsContainer: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: Colors.surface,
    borderRadius: 12,
    marginBottom: 8,
  },
  transactionIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionReason: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
  },
  transactionParty: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  transactionDate: {
    fontSize: 12,
    color: Colors.textSecondary,
    marginTop: 2,
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  transactionAmountText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  coinsLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  emptyState: {
    alignItems: 'center',
    padding: 32,
    backgroundColor: Colors.surface,
    borderRadius: 12,
  },
  emptyStateTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textSecondary,
    marginTop: 16,
  },
  emptyStateText: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginTop: 8,
    textAlign: 'center',
  },
  infoContainer: {
    margin: 16,
    padding: 16,
    backgroundColor: Colors.surface,
    borderRadius: 12,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  infoText: {
    fontSize: 14,
    color: Colors.textSecondary,
    lineHeight: 20,
    marginBottom: 12,
  },
  infoList: {
    marginTop: 8,
  },
  infoItem: {
    fontSize: 14,
    color: Colors.textSecondary,
    marginBottom: 4,
  },
});

export default WalletScreen;
