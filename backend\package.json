{"name": "nowee-api-production", "version": "1.0.0", "description": "API de production pour Nowee", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "build": "echo 'Build completed'", "test": "jest"}, "engines": {"node": "18.x", "npm": "9.x"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "@supabase/supabase-js": "^2.38.4", "openai": "^4.20.1", "twilio": "^4.19.0", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "socket.io": "^4.7.4", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "keywords": ["nowee", "entraide", "api", "production"], "author": "Nowee Team", "license": "MIT"}