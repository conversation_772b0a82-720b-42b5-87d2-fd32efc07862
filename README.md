# 🤖 Nowee - Assistant d'Entraide Locale

**Nowee** est un assistant IA révolutionnaire qui connecte instantanément les besoins locaux aux solutions communautaires via WhatsApp. Conçu pour l'Afrique et les communautés émergentes.

## 🎯 Vision

Transformer chaque besoin en solution grâce à l'intelligence artificielle et la solidarité locale.

## ✨ Fonctionnalités

### 🤖 Bot WhatsApp Intelligent
- **IA contextuelle** avec OpenAI GPT-4
- **Compréhension naturelle** en français local
- **Réponses instantanées** et personnalisées
- **Commandes avancées** pour toutes les fonctions

### 💰 Système Économique
- **NoweeCoins** - Monnaie locale virtuelle
- **Portefeuille intégré** avec historique
- **Transferts instantanés** entre utilisateurs
- **Récompenses automatiques** pour l'entraide

### 🔄 Système de Troc
- **Échanges intelligents** objets/services/temps
- **Matching automatique** des besoins
- **Négociation en temps réel**
- **Propositions personnalisées**

### 🗺️ Géolocalisation
- **Aide de proximité** avec calcul de distance
- **Localisation automatique** depuis les messages
- **Carte interactive** des ressources
- **Optimisation des trajets**

### 📱 Application Mobile (React Native)
- **Interface native** iOS/Android
- **Portefeuille visuel** avec animations
- **Carte interactive** en temps réel
- **Notifications push** intelligentes

## 🏗️ Architecture

```
📱 WhatsApp Bot (Twilio)
    ↓
🤖 IA Nowee (OpenAI GPT-4)
    ↓
🔗 API Backend (Node.js + Express)
    ↓
💾 Base de données (Supabase)
    ↓
📊 Analytics & Monitoring
```

## 🚀 Installation Rapide

### 1. Cloner le projet
```bash
git clone https://github.com/your-username/nowee.git
cd nowee
```

### 2. Installation automatique
```bash
npm run setup
```

### 3. Configuration
```bash
# Éditer les variables d'environnement
cp backend/.env.example backend/.env
# Remplir avec vos clés API
```

### 4. Base de données
```bash
# Créer un projet Supabase sur https://supabase.com
# Exécuter le script SQL dans l'éditeur Supabase
cat backend/database/deploy-supabase.sql
```

### 5. Démarrage
```bash
npm run dev
```

## 📋 Configuration

### Variables d'environnement requises

```env
# Supabase
SUPABASE_URL=your-supabase-url
SUPABASE_ANON_KEY=your-anon-key

# OpenAI
OPENAI_API_KEY=sk-your-key

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your-sid
TWILIO_AUTH_TOKEN=your-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********
```

## 🎮 Utilisation

### Via WhatsApp
1. **Envoyer un message** au numéro Twilio
2. **Décrire votre besoin** en langage naturel
3. **Recevoir des solutions** instantanées
4. **Utiliser les commandes** pour plus d'options

### Commandes disponibles
- `/aide` - Aide générale
- `/portefeuille` - Voir ses NoweeCoins
- `/troc` - Gérer ses trocs
- `/economie` - Statistiques économiques
- `/profil` - Voir son profil
- `/stats` - Statistiques communauté

### Exemples d'utilisation
```
"J'ai besoin d'un plombier à Dakar"
"Où acheter du riz pas cher ?"
"Qui peut m'aider à déménager ?"
"Je propose cours de français contre réparation vélo"
```

## 📱 Application Mobile

### Installation
```bash
cd mobile
npm install

# Android
npm run android

# iOS
npm run ios
```

### Fonctionnalités
- **Portefeuille NoweeCoins** avec interface visuelle
- **Système de troc** avec drag & drop
- **Carte interactive** avec géolocalisation
- **Chat intégré** avec l'IA
- **Notifications push** pour les opportunités

## 🚀 Déploiement Production

### Déploiement automatique
```bash
# Windows
./deploy-windows.ps1

# macOS/Linux
./deploy-unix.sh
```

### Déploiement manuel

#### 1. Backend (Heroku)
```bash
cd backend
heroku create nowee-api-prod
git push heroku main
```

#### 2. Frontend (Vercel)
```bash
cd frontend
vercel --prod
```

#### 3. Mobile (App Stores)
```bash
cd mobile
npm run build:android
npm run build:ios
```

## 🧪 Tests

### Tests unitaires
```bash
npm test
```

### Tests d'intégration
```bash
npm run test:integration
```

### Tests E2E
```bash
npm run test:e2e
```

## 📊 Monitoring

### URLs de production
- **API**: https://nowee-api-prod.herokuapp.com
- **Health**: https://nowee-api-prod.herokuapp.com/health
- **Frontend**: https://nowee-app.vercel.app

### Logs
```bash
# Logs Heroku
heroku logs --tail -a nowee-api-prod

# Logs locaux
tail -f backend/nowee.log
```

## 🤝 Contribution

### Workflow
1. Fork le projet
2. Créer une branche (`git checkout -b feature/amazing-feature`)
3. Commit (`git commit -m 'Add amazing feature'`)
4. Push (`git push origin feature/amazing-feature`)
5. Ouvrir une Pull Request

### Standards
- **ESLint** pour la qualité
- **Prettier** pour le formatage
- **Tests** obligatoires
- **Documentation** à jour

## 🌍 Roadmap

### Phase 1 (Actuelle) ✅
- [x] Bot WhatsApp MVP
- [x] Système économique
- [x] Base de données Supabase
- [x] Déploiement production

### Phase 2 (Q1 2025)
- [ ] Application mobile complète
- [ ] Système de réputation avancé
- [ ] Multi-langues (Wolof, Arabe)
- [ ] Intégration SMS

### Phase 3 (Q2 2025)
- [ ] Blockchain et crypto-monnaies
- [ ] Réalité augmentée
- [ ] Mode mesh (sans internet)
- [ ] Expansion internationale

## 📄 Licence

MIT License - voir [LICENSE](LICENSE)

## 📞 Support

- **Email**: <EMAIL>
- **Discord**: [Communauté Nowee](https://discord.gg/nowee)
- **Issues**: [GitHub Issues](https://github.com/your-username/nowee/issues)

## 🙏 Remerciements

- **OpenAI** pour l'IA GPT-4
- **Twilio** pour WhatsApp Business API
- **Supabase** pour la base de données
- **Communauté open source** pour les outils

---

**🎉 Nowee - L'entraide dans votre poche ! 🎉**

*Fait avec ❤️ pour l'Afrique et le monde*
