import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors } from '../utils/Colors';
import { ApiService } from '../services/ApiService';

interface UserProfile {
  phone: string;
  name?: string;
  reputation: number;
  helps_given: number;
  helps_received: number;
  location?: string;
  member_since: Date;
}

const ProfileScreen: React.FC = () => {
  const [profile, setProfile] = useState<UserProfile>({
    phone: '+221771234567',
    name: 'Utilisateur Nowee',
    reputation: 5.0,
    helps_given: 0,
    helps_received: 0,
    location: 'Dakar, Sénégal',
    member_since: new Date(),
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      const profileData = await ApiService.getUserProfile();
      if (profileData) {
        setProfile(profileData);
      }
    } catch (error) {
      console.error('Erreur chargement profil:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadProfile();
    setRefreshing(false);
  };

  const handleEditProfile = () => {
    Alert.alert(
      'Modifier le profil',
      'Fonctionnalité de modification en développement',
    );
  };

  const handleSettings = () => {
    Alert.alert(
      'Paramètres',
      'Notifications, confidentialité, etc.',
    );
  };

  const handleHelp = () => {
    Alert.alert(
      'Aide & Support',
      'Comment utiliser Nowee, FAQ, contact support',
    );
  };

  const handleAbout = () => {
    Alert.alert(
      'À propos de Nowee',
      'Nowee v1.0.0\n\nAssistant d\'entraide locale pour l\'Afrique.\n\nDéveloppé avec ❤️ pour la communauté.',
    );
  };

  const handleLogout = () => {
    Alert.alert(
      'Déconnexion',
      'Êtes-vous sûr de vouloir vous déconnecter ?',
      [
        { text: 'Annuler', style: 'cancel' },
        { text: 'Déconnexion', style: 'destructive', onPress: () => {
          // Logique de déconnexion
          Alert.alert('Déconnecté', 'À bientôt !');
        }},
      ],
    );
  };

  const getReputationStars = (reputation: number) => {
    const stars = [];
    const fullStars = Math.floor(reputation);
    const hasHalfStar = reputation % 1 >= 0.5;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<Icon key={i} name="star" size={20} color={Colors.accent} />);
    }

    if (hasHalfStar) {
      stars.push(<Icon key="half" name="star-half" size={20} color={Colors.accent} />);
    }

    const emptyStars = 5 - Math.ceil(reputation);
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<Icon key={`empty-${i}`} name="star-border" size={20} color={Colors.textSecondary} />);
    }

    return stars;
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* En-tête du profil */}
      <View style={styles.profileHeader}>
        <View style={styles.avatarContainer}>
          <Icon name="person" size={64} color={Colors.background} />
        </View>
        
        <Text style={styles.userName}>{profile.name}</Text>
        <Text style={styles.userPhone}>{profile.phone}</Text>
        
        {profile.location && (
          <View style={styles.locationContainer}>
            <Icon name="location-on" size={16} color={Colors.background} />
            <Text style={styles.locationText}>{profile.location}</Text>
          </View>
        )}
        
        <TouchableOpacity style={styles.editButton} onPress={handleEditProfile}>
          <Icon name="edit" size={16} color={Colors.primary} />
          <Text style={styles.editButtonText}>Modifier</Text>
        </TouchableOpacity>
      </View>

      {/* Statistiques */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Icon name="favorite" size={32} color={Colors.error} />
          <Text style={styles.statNumber}>{profile.helps_given}</Text>
          <Text style={styles.statLabel}>Aides données</Text>
        </View>

        <View style={styles.statCard}>
          <Icon name="thumb-up" size={32} color={Colors.success} />
          <Text style={styles.statNumber}>{profile.helps_received}</Text>
          <Text style={styles.statLabel}>Aides reçues</Text>
        </View>

        <View style={styles.statCard}>
          <View style={styles.reputationContainer}>
            <View style={styles.starsContainer}>
              {getReputationStars(profile.reputation)}
            </View>
            <Text style={styles.reputationNumber}>{profile.reputation.toFixed(1)}</Text>
          </View>
          <Text style={styles.statLabel}>Réputation</Text>
        </View>
      </View>

      {/* Informations du compte */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Informations du compte</Text>
        
        <View style={styles.infoItem}>
          <Icon name="calendar-today" size={20} color={Colors.textSecondary} />
          <Text style={styles.infoLabel}>Membre depuis</Text>
          <Text style={styles.infoValue}>
            {profile.member_since.toLocaleDateString('fr-FR')}
          </Text>
        </View>

        <View style={styles.infoItem}>
          <Icon name="phone" size={20} color={Colors.textSecondary} />
          <Text style={styles.infoLabel}>Téléphone</Text>
          <Text style={styles.infoValue}>{profile.phone}</Text>
        </View>

        <View style={styles.infoItem}>
          <Icon name="verified" size={20} color={Colors.success} />
          <Text style={styles.infoLabel}>Statut</Text>
          <Text style={[styles.infoValue, { color: Colors.success }]}>Vérifié</Text>
        </View>
      </View>

      {/* Menu d'options */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Options</Text>
        
        <TouchableOpacity style={styles.menuItem} onPress={handleSettings}>
          <Icon name="settings" size={24} color={Colors.textSecondary} />
          <Text style={styles.menuText}>Paramètres</Text>
          <Icon name="chevron-right" size={24} color={Colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={handleHelp}>
          <Icon name="help" size={24} color={Colors.textSecondary} />
          <Text style={styles.menuText}>Aide & Support</Text>
          <Icon name="chevron-right" size={24} color={Colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={handleAbout}>
          <Icon name="info" size={24} color={Colors.textSecondary} />
          <Text style={styles.menuText}>À propos</Text>
          <Icon name="chevron-right" size={24} color={Colors.textSecondary} />
        </TouchableOpacity>

        <TouchableOpacity style={styles.menuItem} onPress={handleLogout}>
          <Icon name="logout" size={24} color={Colors.error} />
          <Text style={[styles.menuText, { color: Colors.error }]}>Déconnexion</Text>
          <Icon name="chevron-right" size={24} color={Colors.error} />
        </TouchableOpacity>
      </View>

      {/* Badges et réalisations */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Badges & Réalisations</Text>
        
        <View style={styles.badgesContainer}>
          <View style={styles.badge}>
            <Icon name="star" size={24} color={Colors.accent} />
            <Text style={styles.badgeText}>Nouveau membre</Text>
          </View>
          
          <View style={[styles.badge, styles.badgeDisabled]}>
            <Icon name="favorite" size={24} color={Colors.textSecondary} />
            <Text style={styles.badgeTextDisabled}>Première aide</Text>
          </View>
          
          <View style={[styles.badge, styles.badgeDisabled]}>
            <Icon name="group" size={24} color={Colors.textSecondary} />
            <Text style={styles.badgeTextDisabled}>Ambassadeur</Text>
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  profileHeader: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: Colors.primary,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  avatarContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.secondary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  userName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.background,
    marginBottom: 4,
  },
  userPhone: {
    fontSize: 16,
    color: Colors.background,
    opacity: 0.9,
    marginBottom: 8,
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  locationText: {
    fontSize: 14,
    color: Colors.background,
    marginLeft: 4,
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.background,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
  },
  editButtonText: {
    fontSize: 14,
    color: Colors.primary,
    marginLeft: 4,
    fontWeight: '500',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    margin: 16,
  },
  statCard: {
    alignItems: 'center',
    backgroundColor: Colors.surface,
    padding: 16,
    borderRadius: 12,
    minWidth: 100,
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  reputationContainer: {
    alignItems: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: 4,
  },
  reputationNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
  },
  section: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 16,
  },
  infoItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.surface,
  },
  infoLabel: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  infoValue: {
    fontSize: 16,
    color: Colors.textSecondary,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.surface,
  },
  menuText: {
    flex: 1,
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  badgesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-around',
  },
  badge: {
    alignItems: 'center',
    backgroundColor: Colors.surface,
    padding: 16,
    borderRadius: 12,
    minWidth: 80,
    marginBottom: 12,
  },
  badgeDisabled: {
    opacity: 0.5,
  },
  badgeText: {
    fontSize: 12,
    color: Colors.text,
    textAlign: 'center',
    marginTop: 8,
  },
  badgeTextDisabled: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default ProfileScreen;
