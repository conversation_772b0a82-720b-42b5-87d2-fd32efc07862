#!/usr/bin/env node

/**
 * Script de démarrage rapide pour Nowee Mobile
 */

const { execSync } = require('child_process');

console.log('🚀 Démarrage de Nowee Mobile...');

try {
  // Démarrer Metro bundler
  console.log('📦 Démarrage du Metro bundler...');
  execSync('npx react-native start', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Erreur démarrage:', error.message);
  console.log('💡 Essayez: npm run android (dans un autre terminal)');
}
