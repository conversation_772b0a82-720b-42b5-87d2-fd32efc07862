/**
 * Service de géolocalisation pour Nowee Mobile
 */

import Geolocation from 'react-native-geolocation-service';
import { PermissionsAndroid, Platform, Alert } from 'react-native';

interface Location {
  latitude: number;
  longitude: number;
  name: string;
}

class LocationService {
  private currentLocation: Location | null = null;

  // Demander les permissions de géolocalisation
  async requestLocationPermission(): Promise<boolean> {
    if (Platform.OS === 'android') {
      try {
        const granted = await PermissionsAndroid.request(
          PermissionsAndroid.PERMISSIONS.ACCESS_FINE_LOCATION,
          {
            title: 'Permission de géolocalisation',
            message: 'Nowee a besoin d\'accéder à votre position pour vous aider localement.',
            buttonNeutral: 'Plus tard',
            buttonNegative: 'Refuser',
            buttonPositive: 'Autoriser',
          }
        );
        return granted === PermissionsAndroid.RESULTS.GRANTED;
      } catch (err) {
        console.warn('Erreur permission géolocalisation:', err);
        return false;
      }
    } else {
      // iOS - les permissions sont gérées automatiquement
      return true;
    }
  }

  // Obtenir la position actuelle
  async getCurrentLocation(): Promise<Location> {
    return new Promise(async (resolve, reject) => {
      // Vérifier les permissions
      const hasPermission = await this.requestLocationPermission();
      if (!hasPermission) {
        // Position par défaut (Dakar)
        const defaultLocation: Location = {
          latitude: 14.6928,
          longitude: -17.4467,
          name: 'Dakar, Sénégal',
        };
        resolve(defaultLocation);
        return;
      }

      Geolocation.getCurrentPosition(
        async (position) => {
          const { latitude, longitude } = position.coords;
          
          try {
            // Géocodage inverse pour obtenir le nom du lieu
            const locationName = await this.reverseGeocode(latitude, longitude);
            
            const location: Location = {
              latitude,
              longitude,
              name: locationName,
            };
            
            this.currentLocation = location;
            resolve(location);
          } catch (error) {
            // En cas d'erreur de géocodage, retourner quand même les coordonnées
            const location: Location = {
              latitude,
              longitude,
              name: `${latitude.toFixed(4)}, ${longitude.toFixed(4)}`,
            };
            
            this.currentLocation = location;
            resolve(location);
          }
        },
        (error) => {
          console.error('Erreur géolocalisation:', error);
          
          // Position par défaut en cas d'erreur
          const defaultLocation: Location = {
            latitude: 14.6928,
            longitude: -17.4467,
            name: 'Dakar, Sénégal',
          };
          
          resolve(defaultLocation);
        },
        {
          enableHighAccuracy: true,
          timeout: 15000,
          maximumAge: 10000,
        }
      );
    });
  }

  // Géocodage inverse (coordonnées -> nom du lieu)
  private async reverseGeocode(latitude: number, longitude: number): Promise<string> {
    try {
      // Utiliser un service de géocodage gratuit
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${latitude}&lon=${longitude}&zoom=10&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'NoweeApp/1.0',
          },
        }
      );

      if (!response.ok) {
        throw new Error('Erreur géocodage');
      }

      const data = await response.json();
      
      if (data && data.address) {
        const { city, town, village, state, country } = data.address;
        const locationName = city || town || village || state || country || 'Position inconnue';
        return locationName;
      }

      return 'Position inconnue';
    } catch (error) {
      console.error('Erreur géocodage inverse:', error);
      
      // Fallback basé sur les coordonnées connues du Sénégal
      if (this.isInSenegal(latitude, longitude)) {
        return this.getSenegalLocation(latitude, longitude);
      }
      
      return 'Position inconnue';
    }
  }

  // Vérifier si les coordonnées sont au Sénégal
  private isInSenegal(latitude: number, longitude: number): boolean {
    // Limites approximatives du Sénégal
    return (
      latitude >= 12.0 && latitude <= 16.7 &&
      longitude >= -17.5 && longitude <= -11.3
    );
  }

  // Obtenir une localisation approximative au Sénégal
  private getSenegalLocation(latitude: number, longitude: number): string {
    // Quelques villes principales du Sénégal avec leurs coordonnées approximatives
    const cities = [
      { name: 'Dakar', lat: 14.6928, lon: -17.4467 },
      { name: 'Thiès', lat: 14.7886, lon: -16.9260 },
      { name: 'Kaolack', lat: 14.1612, lon: -16.0734 },
      { name: 'Saint-Louis', lat: 16.0469, lon: -16.4897 },
      { name: 'Ziguinchor', lat: 12.5681, lon: -16.2719 },
      { name: 'Diourbel', lat: 14.6522, lon: -16.2317 },
      { name: 'Tambacounda', lat: 13.7671, lon: -13.6681 },
    ];

    // Trouver la ville la plus proche
    let closestCity = cities[0];
    let minDistance = this.calculateDistance(latitude, longitude, closestCity.lat, closestCity.lon);

    for (const city of cities) {
      const distance = this.calculateDistance(latitude, longitude, city.lat, city.lon);
      if (distance < minDistance) {
        minDistance = distance;
        closestCity = city;
      }
    }

    return `${closestCity.name}, Sénégal`;
  }

  // Calculer la distance entre deux points (formule de Haversine)
  calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
    const R = 6371; // Rayon de la Terre en km
    const dLat = this.toRadians(lat2 - lat1);
    const dLon = this.toRadians(lon2 - lon1);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c;
  }

  // Convertir degrés en radians
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }

  // Obtenir la dernière position connue
  getLastKnownLocation(): Location | null {
    return this.currentLocation;
  }

  // Surveiller les changements de position
  watchPosition(callback: (location: Location) => void): number {
    return Geolocation.watchPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;
        
        try {
          const locationName = await this.reverseGeocode(latitude, longitude);
          const location: Location = {
            latitude,
            longitude,
            name: locationName,
          };
          
          this.currentLocation = location;
          callback(location);
        } catch (error) {
          console.error('Erreur watch position:', error);
        }
      },
      (error) => {
        console.error('Erreur watch position:', error);
      },
      {
        enableHighAccuracy: true,
        distanceFilter: 100, // Mise à jour tous les 100 mètres
        interval: 30000, // Mise à jour toutes les 30 secondes
      }
    );
  }

  // Arrêter la surveillance de position
  clearWatch(watchId: number): void {
    Geolocation.clearWatch(watchId);
  }
}

// Instance singleton
export const LocationService = new LocationService();
export default LocationService;
