import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors } from '../utils/Colors';
import { ApiService } from '../services/ApiService';
import { LocationService } from '../services/LocationService';

interface UserStats {
  helps_given: number;
  helps_received: number;
  reputation: number;
  coins: number;
}

interface CommunityStats {
  total_helps: number;
  active_helpers: number;
  success_rate: number;
}

const HomeScreen: React.FC = () => {
  const [userStats, setUserStats] = useState<UserStats>({
    helps_given: 0,
    helps_received: 0,
    reputation: 5.0,
    coins: 100,
  });
  const [communityStats, setCommunityStats] = useState<CommunityStats>({
    total_helps: 500,
    active_helpers: 75,
    success_rate: 85,
  });
  const [location, setLocation] = useState<string>('Dakar');
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    loadData();
    getCurrentLocation();
  }, []);

  const loadData = async () => {
    try {
      // Charger les statistiques utilisateur et communauté
      const [userStatsData, communityStatsData] = await Promise.all([
        ApiService.getUserStats(),
        ApiService.getCommunityStats(),
      ]);

      if (userStatsData) setUserStats(userStatsData);
      if (communityStatsData) setCommunityStats(communityStatsData);
    } catch (error) {
      console.error('Erreur chargement données:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const currentLocation = await LocationService.getCurrentLocation();
      setLocation(currentLocation.name || 'Dakar');
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'need':
        Alert.alert('Besoin Express', 'Fonctionnalité en développement');
        break;
      case 'offer':
        Alert.alert('Offre d\'Aide', 'Fonctionnalité en développement');
        break;
      case 'emergency':
        Alert.alert('Urgence', 'Fonctionnalité en développement');
        break;
      case 'voice':
        Alert.alert('Assistant Vocal', 'Fonctionnalité en développement');
        break;
    }
  };

  return (
    <ScrollView
      style={styles.container}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* En-tête de bienvenue */}
      <View style={styles.header}>
        <Text style={styles.greeting}>👋 Bonjour !</Text>
        <Text style={styles.location}>📍 {location}</Text>
      </View>

      {/* Actions rapides */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Actions Rapides</Text>
        <View style={styles.quickActions}>
          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.primary }]}
            onPress={() => handleQuickAction('need')}
          >
            <Icon name="help" size={24} color={Colors.background} />
            <Text style={styles.actionText}>Besoin Express</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.secondary }]}
            onPress={() => handleQuickAction('offer')}
          >
            <Icon name="volunteer-activism" size={24} color={Colors.background} />
            <Text style={styles.actionText}>Offre d'Aide</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.error }]}
            onPress={() => handleQuickAction('emergency')}
          >
            <Icon name="emergency" size={24} color={Colors.background} />
            <Text style={styles.actionText}>Urgence</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.actionButton, { backgroundColor: Colors.accent }]}
            onPress={() => handleQuickAction('voice')}
          >
            <Icon name="mic" size={24} color={Colors.background} />
            <Text style={styles.actionText}>Vocal</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Statistiques personnelles */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Mes Statistiques</Text>
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Icon name="favorite" size={32} color={Colors.error} />
            <Text style={styles.statNumber}>{userStats.helps_given}</Text>
            <Text style={styles.statLabel}>Aides données</Text>
          </View>

          <View style={styles.statCard}>
            <Icon name="thumb-up" size={32} color={Colors.success} />
            <Text style={styles.statNumber}>{userStats.helps_received}</Text>
            <Text style={styles.statLabel}>Aides reçues</Text>
          </View>

          <View style={styles.statCard}>
            <Icon name="star" size={32} color={Colors.accent} />
            <Text style={styles.statNumber}>{userStats.reputation.toFixed(1)}</Text>
            <Text style={styles.statLabel}>Réputation</Text>
          </View>

          <View style={styles.statCard}>
            <Icon name="account-balance-wallet" size={32} color={Colors.primary} />
            <Text style={styles.statNumber}>{userStats.coins}</Text>
            <Text style={styles.statLabel}>NoweeCoins</Text>
          </View>
        </View>
      </View>

      {/* Statistiques communauté */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Communauté Nowee</Text>
        <View style={styles.communityStats}>
          <View style={styles.communityStatRow}>
            <Icon name="group" size={24} color={Colors.primary} />
            <Text style={styles.communityStatText}>
              {communityStats.active_helpers} aidants actifs
            </Text>
          </View>

          <View style={styles.communityStatRow}>
            <Icon name="handshake" size={24} color={Colors.success} />
            <Text style={styles.communityStatText}>
              {communityStats.total_helps} aides réalisées
            </Text>
          </View>

          <View style={styles.communityStatRow}>
            <Icon name="trending-up" size={24} color={Colors.accent} />
            <Text style={styles.communityStatText}>
              {communityStats.success_rate}% de taux de succès
            </Text>
          </View>
        </View>
      </View>

      {/* Besoins récents à proximité */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Besoins à Proximité</Text>
        <View style={styles.needsContainer}>
          <Text style={styles.comingSoon}>
            🚧 Fonctionnalité en développement
          </Text>
          <Text style={styles.comingSoonDesc}>
            Bientôt, vous verrez ici les besoins d'aide près de chez vous !
          </Text>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 20,
    backgroundColor: Colors.primary,
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  greeting: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.background,
    marginBottom: 5,
  },
  location: {
    fontSize: 16,
    color: Colors.background,
    opacity: 0.9,
  },
  section: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  quickActions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  actionText: {
    color: Colors.background,
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    backgroundColor: Colors.surface,
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 12,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: 4,
  },
  communityStats: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: 16,
  },
  communityStatRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  communityStatText: {
    fontSize: 16,
    color: Colors.text,
    marginLeft: 12,
  },
  needsContainer: {
    backgroundColor: Colors.surface,
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
  },
  comingSoon: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.textSecondary,
    marginBottom: 8,
  },
  comingSoonDesc: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
});

export default HomeScreen;
