/**
 * Service principal d'initialisation de l'application Nowee
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { Alert } from 'react-native';
import { ApiService } from './ApiService';
import { LocationService } from './LocationService';

class AppService {
  private isInitialized = false;

  // Initialiser tous les services de l'application
  async initializeServices(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      console.log('🚀 Initialisation des services Nowee...');

      // 1. Vérifier la connectivité API
      await this.checkApiConnectivity();

      // 2. Initialiser la géolocalisation
      await this.initializeLocation();

      // 3. Charger les données utilisateur
      await this.loadUserData();

      // 4. Configurer les notifications (si disponible)
      await this.setupNotifications();

      this.isInitialized = true;
      console.log('✅ Services Nowee initialisés avec succès');

    } catch (error) {
      console.error('❌ Erreur initialisation services:', error);
      throw error;
    }
  }

  // Vérifier la connectivité avec l'API
  private async checkApiConnectivity(): Promise<void> {
    try {
      console.log('🔍 Vérification connectivité API...');
      const health = await ApiService.checkHealth();
      
      if (health && health.status === 'OK') {
        console.log('✅ API Nowee accessible');
        await AsyncStorage.setItem('api_status', 'connected');
      } else {
        throw new Error('API non disponible');
      }
    } catch (error) {
      console.warn('⚠️ API non accessible, mode hors-ligne activé');
      await AsyncStorage.setItem('api_status', 'offline');
      // Ne pas bloquer l'application, continuer en mode hors-ligne
    }
  }

  // Initialiser la géolocalisation
  private async initializeLocation(): Promise<void> {
    try {
      console.log('📍 Initialisation géolocalisation...');
      const location = await LocationService.getCurrentLocation();
      
      if (location) {
        console.log(`✅ Position: ${location.name}`);
        await AsyncStorage.setItem('last_location', JSON.stringify(location));
      }
    } catch (error) {
      console.warn('⚠️ Géolocalisation non disponible:', error);
      // Continuer sans géolocalisation
    }
  }

  // Charger les données utilisateur
  private async loadUserData(): Promise<void> {
    try {
      console.log('👤 Chargement données utilisateur...');
      
      // Vérifier si l'utilisateur existe déjà
      const existingUser = await AsyncStorage.getItem('user_profile');
      
      if (!existingUser) {
        // Créer un profil utilisateur par défaut
        const defaultProfile = {
          phone: '+221771234567', // Numéro par défaut
          name: 'Utilisateur Nowee',
          reputation: 5.0,
          helps_given: 0,
          helps_received: 0,
          coins: 100,
          created_at: new Date().toISOString(),
        };
        
        await AsyncStorage.setItem('user_profile', JSON.stringify(defaultProfile));
        console.log('✅ Profil utilisateur créé');
      } else {
        console.log('✅ Profil utilisateur chargé');
      }

      // Charger le portefeuille
      const wallet = await ApiService.getWallet();
      if (wallet) {
        await AsyncStorage.setItem('user_wallet', JSON.stringify(wallet));
      }

    } catch (error) {
      console.warn('⚠️ Erreur chargement données utilisateur:', error);
    }
  }

  // Configurer les notifications
  private async setupNotifications(): Promise<void> {
    try {
      console.log('🔔 Configuration notifications...');
      
      // Vérifier si les notifications sont autorisées
      const notificationsEnabled = await AsyncStorage.getItem('notifications_enabled');
      
      if (notificationsEnabled === null) {
        // Première fois, demander l'autorisation
        Alert.alert(
          'Notifications Nowee',
          'Autorisez les notifications pour être alerté des opportunités d\'aide près de chez vous.',
          [
            {
              text: 'Plus tard',
              onPress: () => AsyncStorage.setItem('notifications_enabled', 'false'),
            },
            {
              text: 'Autoriser',
              onPress: () => {
                AsyncStorage.setItem('notifications_enabled', 'true');
                this.enableNotifications();
              },
            },
          ]
        );
      } else if (notificationsEnabled === 'true') {
        await this.enableNotifications();
      }

    } catch (error) {
      console.warn('⚠️ Erreur configuration notifications:', error);
    }
  }

  // Activer les notifications
  private async enableNotifications(): Promise<void> {
    try {
      // Configuration des notifications push
      // (Implémentation dépendante de react-native-push-notification)
      console.log('✅ Notifications activées');
    } catch (error) {
      console.warn('⚠️ Erreur activation notifications:', error);
    }
  }

  // Vérifier si l'application est en mode hors-ligne
  async isOfflineMode(): Promise<boolean> {
    try {
      const apiStatus = await AsyncStorage.getItem('api_status');
      return apiStatus === 'offline';
    } catch (error) {
      return true; // Par défaut, considérer comme hors-ligne
    }
  }

  // Synchroniser les données quand la connexion revient
  async syncData(): Promise<void> {
    try {
      console.log('🔄 Synchronisation des données...');
      
      // Vérifier la connectivité
      await this.checkApiConnectivity();
      
      const isOffline = await this.isOfflineMode();
      if (isOffline) {
        console.log('⚠️ Toujours hors-ligne, synchronisation reportée');
        return;
      }

      // Synchroniser les données locales avec le serveur
      await this.loadUserData();
      
      console.log('✅ Synchronisation terminée');
    } catch (error) {
      console.error('❌ Erreur synchronisation:', error);
    }
  }

  // Nettoyer les données de l'application
  async clearAppData(): Promise<void> {
    try {
      console.log('🧹 Nettoyage des données...');
      
      const keysToKeep = ['notifications_enabled']; // Garder certaines préférences
      const allKeys = await AsyncStorage.getAllKeys();
      const keysToRemove = allKeys.filter(key => !keysToKeep.includes(key));
      
      await AsyncStorage.multiRemove(keysToRemove);
      
      this.isInitialized = false;
      console.log('✅ Données nettoyées');
    } catch (error) {
      console.error('❌ Erreur nettoyage données:', error);
    }
  }

  // Obtenir les informations de l'application
  getAppInfo(): object {
    return {
      version: '1.0.0',
      name: 'Nowee Mobile',
      description: 'Assistant d\'entraide locale',
      initialized: this.isInitialized,
    };
  }
}

// Instance singleton
const appService = new AppService();

// Fonction d'initialisation exportée
export const initializeServices = () => appService.initializeServices();
export const isOfflineMode = () => appService.isOfflineMode();
export const syncData = () => appService.syncData();
export const clearAppData = () => appService.clearAppData();
export const getAppInfo = () => appService.getAppInfo();

export default appService;
