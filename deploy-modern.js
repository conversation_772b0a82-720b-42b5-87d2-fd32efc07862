#!/usr/bin/env node

/**
 * Script de déploiement moderne pour Nowee
 * Support : Render.com, Railway.app, Vercel
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import readline from 'readline';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, cwd = process.cwd()) {
  try {
    log(`Exécution: ${command}`, 'cyan');
    execSync(command, { stdio: 'inherit', cwd });
    return true;
  } catch (error) {
    log(`❌ Erreur: ${error.message}`, 'red');
    return false;
  }
}

// Interface pour choisir la plateforme
async function choosePlatform() {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  return new Promise((resolve) => {
    log('\n🚀 Choisissez votre plateforme de déploiement:', 'blue');
    log('1. Render.com (Recommandé - Gratuit)', 'green');
    log('2. Railway.app (Moderne - Gratuit)', 'green');
    log('3. Vercel (Frontend uniquement)', 'yellow');
    log('4. Heroku (Classique)', 'yellow');
    
    rl.question('\nVotre choix (1-4): ', (answer) => {
      rl.close();
      resolve(parseInt(answer));
    });
  });
}

// Vérifier les prérequis
function checkPrerequisites() {
  log('🔍 Vérification des prérequis...', 'blue');
  
  const commands = [
    { cmd: 'git --version', name: 'Git' },
    { cmd: 'node --version', name: 'Node.js' },
    { cmd: 'npm --version', name: 'npm' }
  ];

  for (const { cmd, name } of commands) {
    try {
      execSync(cmd, { stdio: 'pipe' });
      log(`✅ ${name} installé`, 'green');
    } catch (error) {
      log(`❌ ${name} non installé`, 'red');
      return false;
    }
  }

  return true;
}

// Déploiement sur Render.com
async function deployToRender() {
  log('🎨 Déploiement sur Render.com...', 'blue');
  
  // Vérifier si le CLI Render est installé
  try {
    execSync('render --version', { stdio: 'pipe' });
  } catch (error) {
    log('📦 Installation du CLI Render...', 'yellow');
    if (!execCommand('npm install -g @render/cli')) {
      log('❌ Impossible d\'installer le CLI Render', 'red');
      log('💡 Installez manuellement: npm install -g @render/cli', 'yellow');
      return false;
    }
  }

  // Vérifier la configuration
  if (!fs.existsSync('render.yaml')) {
    log('❌ Fichier render.yaml manquant', 'red');
    return false;
  }

  log('📋 Instructions pour Render.com:', 'cyan');
  log('1. Connectez-vous sur https://render.com', 'yellow');
  log('2. Connectez votre repository GitHub', 'yellow');
  log('3. Render détectera automatiquement render.yaml', 'yellow');
  log('4. Configurez les variables d\'environnement dans le dashboard', 'yellow');
  log('5. Le déploiement se fera automatiquement', 'yellow');

  // Préparer le commit pour Render
  if (!execCommand('git add .')) return false;
  if (!execCommand('git commit -m "Deploy to Render" || true')) return false;
  
  log('✅ Prêt pour Render! Poussez vers GitHub pour déclencher le déploiement', 'green');
  return true;
}

// Déploiement sur Railway.app
async function deployToRailway() {
  log('🚂 Déploiement sur Railway.app...', 'blue');
  
  // Vérifier si le CLI Railway est installé
  try {
    execSync('railway --version', { stdio: 'pipe' });
  } catch (error) {
    log('📦 Installation du CLI Railway...', 'yellow');
    if (!execCommand('npm install -g @railway/cli')) {
      log('❌ Impossible d\'installer le CLI Railway', 'red');
      log('💡 Installez manuellement: npm install -g @railway/cli', 'yellow');
      return false;
    }
  }

  // Login Railway
  log('🔐 Connexion à Railway...', 'yellow');
  if (!execCommand('railway login')) {
    log('❌ Échec de la connexion Railway', 'red');
    return false;
  }

  // Créer un nouveau projet
  log('📦 Création du projet Railway...', 'yellow');
  if (!execCommand('railway init nowee-api')) {
    log('⚠️ Projet peut-être déjà existant, continuons...', 'yellow');
  }

  // Lier le projet
  if (!execCommand('railway link')) {
    log('❌ Échec de liaison du projet', 'red');
    return false;
  }

  // Configurer les variables d'environnement
  log('⚙️ Configuration des variables d\'environnement...', 'yellow');
  const envFile = path.join('backend', '.env');
  
  if (fs.existsSync(envFile)) {
    const envContent = fs.readFileSync(envFile, 'utf8');
    const envVars = envContent
      .split('\n')
      .filter(line => line.includes('=') && !line.startsWith('#'))
      .map(line => {
        const [key, ...valueParts] = line.split('=');
        return { key: key.trim(), value: valueParts.join('=').trim() };
      });

    for (const { key, value } of envVars) {
      if (value && !value.startsWith('your-')) {
        execCommand(`railway variables set ${key}="${value}"`);
      }
    }
  }

  // Déployer
  log('🚀 Déploiement sur Railway...', 'yellow');
  if (!execCommand('railway up')) {
    log('❌ Échec du déploiement Railway', 'red');
    return false;
  }

  log('✅ Déployé avec succès sur Railway!', 'green');
  
  // Obtenir l'URL
  try {
    const url = execSync('railway status --json', { encoding: 'utf8' });
    const status = JSON.parse(url);
    if (status.deployments && status.deployments[0]) {
      log(`🌐 URL: ${status.deployments[0].url}`, 'cyan');
    }
  } catch (error) {
    log('⚠️ Impossible de récupérer l\'URL automatiquement', 'yellow');
    log('💡 Utilisez: railway status', 'yellow');
  }

  return true;
}

// Déploiement sur Vercel
async function deployToVercel() {
  log('▲ Déploiement sur Vercel...', 'blue');
  
  // Vérifier si le CLI Vercel est installé
  try {
    execSync('vercel --version', { stdio: 'pipe' });
  } catch (error) {
    log('📦 Installation du CLI Vercel...', 'yellow');
    if (!execCommand('npm install -g vercel')) {
      log('❌ Impossible d\'installer le CLI Vercel', 'red');
      return false;
    }
  }

  // Créer un projet frontend simple pour Vercel
  const frontendDir = 'frontend';
  if (!fs.existsSync(frontendDir)) {
    log('📁 Création du frontend pour Vercel...', 'yellow');
    fs.mkdirSync(frontendDir, { recursive: true });
    
    // Créer un index.html simple
    const indexHtml = `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Nowee - Entraide Locale</title>
    <style>
        body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
        .logo { font-size: 3em; margin-bottom: 20px; }
        .subtitle { color: #666; margin-bottom: 30px; }
        .cta { background: #2E7D32; color: white; padding: 15px 30px; border: none; border-radius: 5px; font-size: 1.2em; cursor: pointer; }
    </style>
</head>
<body>
    <div class="logo">🤝 Nowee</div>
    <h1>Assistant d'Entraide Locale</h1>
    <p class="subtitle">Connectez-vous via WhatsApp pour commencer</p>
    <button class="cta" onclick="window.open('https://wa.me/14155238886?text=Bonjour')">
        Démarrer sur WhatsApp
    </button>
    <p><small>API Status: <span id="status">Vérification...</span></small></p>
    
    <script>
        // Vérifier le statut de l'API
        fetch('/api/health')
            .then(r => r.json())
            .then(data => {
                document.getElementById('status').textContent = data.status === 'OK' ? '✅ En ligne' : '❌ Hors ligne';
            })
            .catch(() => {
                document.getElementById('status').textContent = '❌ Hors ligne';
            });
    </script>
</body>
</html>`;
    
    fs.writeFileSync(path.join(frontendDir, 'index.html'), indexHtml);
    
    // Créer vercel.json
    const vercelConfig = {
      "version": 2,
      "name": "nowee-frontend",
      "builds": [
        { "src": "index.html", "use": "@vercel/static" }
      ],
      "routes": [
        { "src": "/api/(.*)", "dest": "https://your-backend-url.com/api/$1" },
        { "src": "/(.*)", "dest": "/index.html" }
      ]
    };
    
    fs.writeFileSync(path.join(frontendDir, 'vercel.json'), JSON.stringify(vercelConfig, null, 2));
  }

  // Déployer sur Vercel
  if (!execCommand('vercel --prod', frontendDir)) {
    log('❌ Échec du déploiement Vercel', 'red');
    return false;
  }

  log('✅ Frontend déployé sur Vercel!', 'green');
  log('⚠️ N\'oubliez pas de déployer le backend séparément', 'yellow');
  return true;
}

// Déploiement sur Heroku (classique)
async function deployToHeroku() {
  log('🟣 Déploiement sur Heroku...', 'blue');
  
  // Vérifier si le CLI Heroku est installé
  try {
    execSync('heroku --version', { stdio: 'pipe' });
  } catch (error) {
    log('❌ Heroku CLI non installé', 'red');
    log('💡 Installez depuis: https://devcenter.heroku.com/articles/heroku-cli', 'yellow');
    return false;
  }

  const backendDir = path.join(process.cwd(), 'backend');
  
  // Créer l'application Heroku
  try {
    execSync('heroku apps:info nowee-api-prod', { stdio: 'pipe' });
    log('✅ Application Heroku existe déjà', 'green');
  } catch (error) {
    log('📦 Création de l\'application Heroku...', 'yellow');
    if (!execCommand('heroku create nowee-api-prod', backendDir)) {
      return false;
    }
  }

  // Configurer les variables d'environnement
  log('⚙️ Configuration des variables d\'environnement...', 'yellow');
  const envFile = path.join(backendDir, '.env');
  
  if (fs.existsSync(envFile)) {
    const envContent = fs.readFileSync(envFile, 'utf8');
    const envVars = envContent
      .split('\n')
      .filter(line => line.includes('=') && !line.startsWith('#'))
      .map(line => {
        const [key, ...valueParts] = line.split('=');
        return `${key}=${valueParts.join('=')}`;
      });

    for (const envVar of envVars) {
      execCommand(`heroku config:set ${envVar}`, backendDir);
    }
  }

  // Déployer
  log('🚀 Déploiement sur Heroku...', 'yellow');
  if (!execCommand('git add .', backendDir)) return false;
  if (!execCommand('git commit -m "Deploy to Heroku" || true', backendDir)) return false;
  if (!execCommand('git push heroku main', backendDir)) return false;

  log('✅ Déployé avec succès sur Heroku!', 'green');
  log('🌐 URL: https://nowee-api-prod.herokuapp.com', 'cyan');
  return true;
}

// Fonction principale
async function main() {
  log('🚀 Déploiement Nowee - Plateformes Modernes', 'bright');
  log('===========================================', 'bright');

  if (!checkPrerequisites()) {
    process.exit(1);
  }

  const platform = await choosePlatform();
  
  let success = false;
  
  switch (platform) {
    case 1:
      success = await deployToRender();
      break;
    case 2:
      success = await deployToRailway();
      break;
    case 3:
      success = await deployToVercel();
      break;
    case 4:
      success = await deployToHeroku();
      break;
    default:
      log('❌ Choix invalide', 'red');
      process.exit(1);
  }

  if (success) {
    log('\n🎉 Déploiement réussi!', 'green');
    log('📱 Testez votre bot WhatsApp maintenant!', 'cyan');
  } else {
    log('\n❌ Échec du déploiement', 'red');
    process.exit(1);
  }
}

// Exécution
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(console.error);
}
