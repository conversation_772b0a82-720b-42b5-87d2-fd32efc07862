/**
 * Service de notifications intelligentes Nowee
 * Gère les notifications push, locales et les rappels
 */

import PushNotification, { Importance } from 'react-native-push-notification';
import { Platform, Alert } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { ApiService } from './ApiService';

interface NotificationData {
  id: string;
  title: string;
  message: string;
  type: 'help_request' | 'help_offer' | 'reminder' | 'match_found' | 'coins_received';
  data?: any;
  scheduledTime?: Date;
}

class NotificationService {
  private isInitialized = false;

  // Initialiser le service de notifications
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Configuration pour Android
      PushNotification.createChannel(
        {
          channelId: 'nowee-default',
          channelName: 'Nowee Notifications',
          channelDescription: 'Notifications générales de Nowee',
          playSound: true,
          soundName: 'default',
          importance: Importance.HIGH,
          vibrate: true,
        },
        (created) => console.log(`Canal créé: ${created}`)
      );

      PushNotification.createChannel(
        {
          channelId: 'nowee-urgent',
          channelName: 'Nowee Urgent',
          channelDescription: 'Notifications urgentes de Nowee',
          playSound: true,
          soundName: 'default',
          importance: Importance.HIGH,
          vibrate: true,
        },
        (created) => console.log(`Canal urgent créé: ${created}`)
      );

      // Configuration générale
      PushNotification.configure({
        onRegister: (token) => {
          console.log('Token FCM:', token);
          this.saveDeviceToken(token.token);
        },

        onNotification: (notification) => {
          console.log('Notification reçue:', notification);
          this.handleNotificationReceived(notification);
        },

        onAction: (notification) => {
          console.log('Action notification:', notification);
          this.handleNotificationAction(notification);
        },

        onRegistrationError: (err) => {
          console.error('Erreur registration:', err);
        },

        permissions: {
          alert: true,
          badge: true,
          sound: true,
        },

        popInitialNotification: true,
        requestPermissions: Platform.OS === 'ios',
      });

      // Démarrer les rappels intelligents
      this.startIntelligentReminders();

      this.isInitialized = true;
      console.log('✅ Service de notifications initialisé');
    } catch (error) {
      console.error('❌ Erreur initialisation notifications:', error);
    }
  }

  // Sauvegarder le token de l'appareil
  private async saveDeviceToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem('device_token', token);
      // Envoyer le token au backend pour les notifications push
      // await ApiService.updateDeviceToken(token);
    } catch (error) {
      console.error('Erreur sauvegarde token:', error);
    }
  }

  // Gérer la réception d'une notification
  private handleNotificationReceived(notification: any): void {
    // Logique personnalisée selon le type de notification
    if (notification.data?.type === 'help_request') {
      this.handleHelpRequestNotification(notification);
    } else if (notification.data?.type === 'match_found') {
      this.handleMatchFoundNotification(notification);
    }
  }

  // Gérer les actions sur les notifications
  private handleNotificationAction(notification: any): void {
    const { action, data } = notification;
    
    switch (action) {
      case 'accept_help':
        this.acceptHelpRequest(data.requestId);
        break;
      case 'view_match':
        this.viewMatch(data.matchId);
        break;
      case 'snooze_reminder':
        this.snoozeReminder(data.reminderId);
        break;
    }
  }

  // Envoyer une notification locale
  sendLocalNotification(notificationData: NotificationData): void {
    const { id, title, message, type, data, scheduledTime } = notificationData;

    const channelId = type === 'help_request' ? 'nowee-urgent' : 'nowee-default';
    
    const notification = {
      id: id,
      channelId: channelId,
      title: title,
      message: message,
      playSound: true,
      soundName: 'default',
      importance: 'high',
      vibrate: true,
      vibration: 300,
      userInfo: data,
      actions: this.getActionsForType(type),
    };

    if (scheduledTime) {
      // Notification programmée
      PushNotification.localNotificationSchedule({
        ...notification,
        date: scheduledTime,
      });
    } else {
      // Notification immédiate
      PushNotification.localNotification(notification);
    }
  }

  // Obtenir les actions selon le type de notification
  private getActionsForType(type: string): string[] {
    switch (type) {
      case 'help_request':
        return ['accept_help', 'view_details'];
      case 'match_found':
        return ['view_match', 'dismiss'];
      case 'reminder':
        return ['snooze_reminder', 'mark_done'];
      default:
        return ['view'];
    }
  }

  // Démarrer les rappels intelligents
  private async startIntelligentReminders(): Promise<void> {
    // Vérifier les besoins non résolus toutes les heures
    setInterval(async () => {
      await this.checkUnresolvedNeeds();
    }, 60 * 60 * 1000); // 1 heure

    // Vérifier les opportunités d'aide toutes les 30 minutes
    setInterval(async () => {
      await this.checkHelpOpportunities();
    }, 30 * 60 * 1000); // 30 minutes

    // Rappel quotidien d'engagement
    this.scheduleDailyEngagementReminder();
  }

  // Vérifier les besoins non résolus
  private async checkUnresolvedNeeds(): Promise<void> {
    try {
      const unresolvedNeeds = await ApiService.getUnresolvedNeeds();
      
      for (const need of unresolvedNeeds) {
        const timeSinceCreated = Date.now() - new Date(need.created_at).getTime();
        const hoursElapsed = timeSinceCreated / (1000 * 60 * 60);

        // Rappel après 2 heures, puis toutes les 6 heures
        if (hoursElapsed >= 2 && hoursElapsed % 6 < 1) {
          this.sendLocalNotification({
            id: `reminder-${need.id}`,
            title: '🔔 Besoin toujours actif',
            message: `Votre demande "${need.description.substring(0, 50)}..." n'a pas encore trouvé de solution. Voulez-vous la modifier ou l'annuler ?`,
            type: 'reminder',
            data: { needId: need.id, type: 'unresolved_need' },
          });
        }
      }
    } catch (error) {
      console.error('Erreur vérification besoins non résolus:', error);
    }
  }

  // Vérifier les opportunités d'aide
  private async checkHelpOpportunities(): Promise<void> {
    try {
      const nearbyNeeds = await ApiService.getNearbyNeeds(0, 0, 5); // 5km de rayon
      const userPreferences = await this.getUserNotificationPreferences();

      for (const need of nearbyNeeds) {
        // Vérifier si l'utilisateur peut aider selon ses préférences
        if (this.matchesUserPreferences(need, userPreferences)) {
          this.sendLocalNotification({
            id: `opportunity-${need.id}`,
            title: '🤝 Opportunité d\'aide près de vous',
            message: `Quelqu\'un a besoin d\'aide : "${need.description.substring(0, 60)}..." à ${need.distance?.toFixed(1)}km`,
            type: 'help_offer',
            data: { needId: need.id, type: 'help_opportunity' },
          });
        }
      }
    } catch (error) {
      console.error('Erreur vérification opportunités:', error);
    }
  }

  // Programmer le rappel quotidien d'engagement
  private scheduleDailyEngagementReminder(): void {
    const now = new Date();
    const reminderTime = new Date();
    reminderTime.setHours(18, 0, 0, 0); // 18h00

    // Si c'est déjà passé aujourd'hui, programmer pour demain
    if (reminderTime <= now) {
      reminderTime.setDate(reminderTime.getDate() + 1);
    }

    this.sendLocalNotification({
      id: 'daily-engagement',
      title: '🌟 Comment allez-vous aujourd\'hui ?',
      message: 'Avez-vous aidé quelqu\'un ou avez-vous besoin d\'aide ? Nowee est là pour vous !',
      type: 'reminder',
      data: { type: 'daily_engagement' },
      scheduledTime: reminderTime,
    });
  }

  // Obtenir les préférences de notification de l'utilisateur
  private async getUserNotificationPreferences(): Promise<any> {
    try {
      const preferences = await AsyncStorage.getItem('notification_preferences');
      return preferences ? JSON.parse(preferences) : {
        helpRequests: true,
        matchFound: true,
        coinsReceived: true,
        dailyReminders: true,
        maxDistance: 5, // km
        categories: ['all'],
      };
    } catch (error) {
      console.error('Erreur récupération préférences:', error);
      return {};
    }
  }

  // Vérifier si un besoin correspond aux préférences de l'utilisateur
  private matchesUserPreferences(need: any, preferences: any): boolean {
    // Vérifier la distance
    if (need.distance > preferences.maxDistance) {
      return false;
    }

    // Vérifier les catégories
    if (preferences.categories && !preferences.categories.includes('all')) {
      if (!preferences.categories.includes(need.category)) {
        return false;
      }
    }

    return true;
  }

  // Gérer une notification de demande d'aide
  private handleHelpRequestNotification(notification: any): void {
    // Logique spécifique pour les demandes d'aide
    console.log('Demande d\'aide reçue:', notification);
  }

  // Gérer une notification de correspondance trouvée
  private handleMatchFoundNotification(notification: any): void {
    // Logique spécifique pour les correspondances
    console.log('Correspondance trouvée:', notification);
  }

  // Accepter une demande d'aide
  private async acceptHelpRequest(requestId: string): Promise<void> {
    try {
      await ApiService.acceptHelpRequest(requestId);
      this.sendLocalNotification({
        id: `accepted-${requestId}`,
        title: '✅ Aide acceptée',
        message: 'Vous avez accepté d\'aider ! La personne a été notifiée.',
        type: 'help_offer',
      });
    } catch (error) {
      console.error('Erreur acceptation aide:', error);
    }
  }

  // Voir une correspondance
  private viewMatch(matchId: string): void {
    // Navigation vers l'écran de correspondance
    console.log('Voir correspondance:', matchId);
  }

  // Reporter un rappel
  private snoozeReminder(reminderId: string): void {
    // Reporter le rappel de 1 heure
    const snoozeTime = new Date(Date.now() + 60 * 60 * 1000);
    
    this.sendLocalNotification({
      id: `snoozed-${reminderId}`,
      title: '⏰ Rappel reporté',
      message: 'Nous vous rappellerons dans 1 heure.',
      type: 'reminder',
      scheduledTime: snoozeTime,
    });
  }

  // Mettre à jour les préférences de notification
  async updateNotificationPreferences(preferences: any): Promise<void> {
    try {
      await AsyncStorage.setItem('notification_preferences', JSON.stringify(preferences));
    } catch (error) {
      console.error('Erreur mise à jour préférences:', error);
    }
  }

  // Annuler toutes les notifications
  cancelAllNotifications(): void {
    PushNotification.cancelAllLocalNotifications();
  }

  // Annuler une notification spécifique
  cancelNotification(notificationId: string): void {
    PushNotification.cancelLocalNotifications({ id: notificationId });
  }
}

// Instance singleton
export const NotificationService = new NotificationService();
export default NotificationService;
