/**
 * Service API pour communiquer avec le backend Nowee
 */

import axios, { AxiosInstance, AxiosResponse } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';

class ApiService {
  private api: AxiosInstance;
  private baseURL: string;

  constructor() {
    // URL de l'API (à configurer selon l'environnement)
    this.baseURL = __DEV__ 
      ? 'http://localhost:3000' 
      : 'https://nowee-api-prod.herokuapp.com';

    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Intercepteur pour ajouter le token d'authentification
    this.api.interceptors.request.use(
      async (config) => {
        const token = await AsyncStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Intercepteur pour gérer les réponses
    this.api.interceptors.response.use(
      (response: AxiosResponse) => {
        return response;
      },
      (error) => {
        console.error('Erreur API:', error.response?.data || error.message);
        return Promise.reject(error);
      }
    );
  }

  // Vérifier la santé de l'API
  async checkHealth(): Promise<any> {
    try {
      const response = await this.api.get('/health');
      return response.data;
    } catch (error) {
      console.error('Erreur health check:', error);
      throw error;
    }
  }

  // Envoyer un message au chat IA
  async sendChatMessage(message: string): Promise<any> {
    try {
      // Simuler l'envoi via WhatsApp webhook
      const response = await this.api.post('/webhook', {
        Body: message,
        From: 'whatsapp:+221771234567', // Numéro simulé
      });
      
      // Parser la réponse TwiML pour extraire le message
      const twimlResponse = response.data;
      const messageMatch = twimlResponse.match(/<Message>(.*?)<\/Message>/s);
      const aiMessage = messageMatch ? messageMatch[1].trim() : 'Réponse non disponible';
      
      return { message: aiMessage };
    } catch (error) {
      console.error('Erreur envoi message:', error);
      throw error;
    }
  }

  // Récupérer le portefeuille utilisateur
  async getWallet(phone: string = '+221771234567'): Promise<any> {
    try {
      const response = await this.api.get(`/api/economy/wallet/${phone.replace('+', '')}`);
      return response.data;
    } catch (error) {
      console.error('Erreur récupération wallet:', error);
      // Retourner des données par défaut en cas d'erreur
      return {
        coins: 100,
        total_earned: 100,
        total_spent: 0,
        phone: phone,
      };
    }
  }

  // Récupérer les statistiques économiques
  async getEconomyStats(): Promise<any> {
    try {
      const response = await this.api.get('/api/economy/stats');
      return response.data;
    } catch (error) {
      console.error('Erreur récupération stats économiques:', error);
      return {
        total_coins: 10000,
        active_users: 150,
        transactions_today: 25,
        average_transaction: 15,
      };
    }
  }

  // Transférer des coins
  async transferCoins(fromPhone: string, toPhone: string, amount: number, reason: string): Promise<any> {
    try {
      const response = await this.api.post('/api/economy/transfer', {
        fromPhone,
        toPhone,
        amount,
        reason,
      });
      return response.data;
    } catch (error) {
      console.error('Erreur transfert coins:', error);
      throw error;
    }
  }

  // Récupérer les statistiques utilisateur
  async getUserStats(phone: string = '+221771234567'): Promise<any> {
    try {
      // Simuler les statistiques utilisateur
      return {
        helps_given: 5,
        helps_received: 3,
        reputation: 4.8,
        coins: 150,
      };
    } catch (error) {
      console.error('Erreur récupération stats utilisateur:', error);
      return {
        helps_given: 0,
        helps_received: 0,
        reputation: 5.0,
        coins: 100,
      };
    }
  }

  // Récupérer les statistiques communauté
  async getCommunityStats(): Promise<any> {
    try {
      // Simuler les statistiques communauté
      return {
        total_helps: 500,
        active_helpers: 75,
        success_rate: 85,
        average_response_time: 12,
      };
    } catch (error) {
      console.error('Erreur récupération stats communauté:', error);
      return {
        total_helps: 500,
        active_helpers: 75,
        success_rate: 85,
        average_response_time: 12,
      };
    }
  }

  // Récupérer le profil utilisateur
  async getUserProfile(phone: string = '+221771234567'): Promise<any> {
    try {
      // Simuler le profil utilisateur
      return {
        phone: phone,
        name: 'Utilisateur Nowee',
        reputation: 4.8,
        helps_given: 5,
        helps_received: 3,
        location: 'Dakar, Sénégal',
        member_since: new Date('2024-01-01'),
      };
    } catch (error) {
      console.error('Erreur récupération profil:', error);
      return {
        phone: phone,
        name: 'Utilisateur Nowee',
        reputation: 5.0,
        helps_given: 0,
        helps_received: 0,
        location: 'Dakar, Sénégal',
        member_since: new Date(),
      };
    }
  }

  // Récupérer les transactions
  async getTransactions(phone: string = '+221771234567'): Promise<any[]> {
    try {
      // Simuler les transactions
      return [
        {
          id: '1',
          amount: 10,
          reason: 'Aide pour déménagement',
          type: 'received',
          date: new Date('2024-01-15'),
          otherParty: '+221771234568',
        },
        {
          id: '2',
          amount: 5,
          reason: 'Partage d\'information',
          type: 'sent',
          date: new Date('2024-01-14'),
          otherParty: '+221771234569',
        },
      ];
    } catch (error) {
      console.error('Erreur récupération transactions:', error);
      return [];
    }
  }

  // Récupérer les besoins à proximité
  async getNearbyNeeds(latitude: number, longitude: number, radius: number = 5): Promise<any[]> {
    try {
      // Simuler les besoins à proximité
      return [
        {
          id: '1',
          description: 'Besoin d\'aide pour déménagement',
          location: 'Dakar Plateau',
          distance: 2.5,
          urgency: 'normal',
          coins_offered: 15,
        },
        {
          id: '2',
          description: 'Recherche plombier',
          location: 'Almadies',
          distance: 4.2,
          urgency: 'urgent',
          coins_offered: 25,
        },
      ];
    } catch (error) {
      console.error('Erreur récupération besoins:', error);
      return [];
    }
  }

  // Créer un nouveau besoin
  async createNeed(description: string, location: string, urgency: string = 'normal', coinsOffered: number = 0): Promise<any> {
    try {
      // Simuler la création d'un besoin
      return {
        id: Date.now().toString(),
        description,
        location,
        urgency,
        coins_offered: coinsOffered,
        status: 'active',
        created_at: new Date(),
      };
    } catch (error) {
      console.error('Erreur création besoin:', error);
      throw error;
    }
  }

  // Créer une nouvelle offre
  async createOffer(description: string, location: string, coinsRequested: number = 0): Promise<any> {
    try {
      // Simuler la création d'une offre
      return {
        id: Date.now().toString(),
        description,
        location,
        coins_requested: coinsRequested,
        status: 'active',
        created_at: new Date(),
      };
    } catch (error) {
      console.error('Erreur création offre:', error);
      throw error;
    }
  }
}

// Instance singleton
export const ApiService = new ApiService();
export default ApiService;
