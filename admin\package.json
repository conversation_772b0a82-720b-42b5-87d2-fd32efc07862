{"name": "nowee-admin", "version": "1.0.0", "description": "Tableau de bord administrateur Nowee", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.8.1", "axios": "^1.6.0", "recharts": "^2.8.0", "lucide-react": "^0.294.0", "@tanstack/react-query": "^5.8.4", "date-fns": "^2.30.0", "clsx": "^2.0.0"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8", "typescript": "^5.2.2", "eslint": "^8.55.0", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32"}, "keywords": ["nowee", "admin", "dashboard", "react", "typescript"], "author": "Nowee Team", "license": "MIT"}