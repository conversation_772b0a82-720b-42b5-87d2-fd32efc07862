import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Colors } from '../utils/Colors';
import { LocationService } from '../services/LocationService';

interface Location {
  latitude: number;
  longitude: number;
  name: string;
}

const MapScreen: React.FC = () => {
  const [currentLocation, setCurrentLocation] = useState<Location | null>(null);
  const [loading, setLoading] = useState(true);
  const [mapReady, setMapReady] = useState(false);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  const getCurrentLocation = async () => {
    try {
      setLoading(true);
      const location = await LocationService.getCurrentLocation();
      setCurrentLocation(location);
      setMapReady(true);
    } catch (error) {
      console.error('Erreur géolocalisation:', error);
      Alert.alert(
        'Géolocalisation',
        'Impossible d\'obtenir votre position. Vérifiez les permissions.',
      );
      // Position par défaut (Dakar)
      setCurrentLocation({
        latitude: 14.6928,
        longitude: -17.4467,
        name: 'Dakar, Sénégal',
      });
      setMapReady(true);
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshLocation = () => {
    getCurrentLocation();
  };

  const handleFilterToggle = (filter: string) => {
    Alert.alert('Filtre', `Filtre "${filter}" en développement`);
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
        <Text style={styles.loadingText}>Localisation en cours...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* En-tête avec localisation */}
      <View style={styles.header}>
        <View style={styles.locationInfo}>
          <Icon name="location-on" size={24} color={Colors.primary} />
          <Text style={styles.locationText}>
            {currentLocation?.name || 'Position inconnue'}
          </Text>
        </View>
        <TouchableOpacity
          style={styles.refreshButton}
          onPress={handleRefreshLocation}
        >
          <Icon name="refresh" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Zone de carte (placeholder) */}
      <View style={styles.mapContainer}>
        <View style={styles.mapPlaceholder}>
          <Icon name="map" size={64} color={Colors.textSecondary} />
          <Text style={styles.mapPlaceholderTitle}>
            🗺️ Carte Interactive
          </Text>
          <Text style={styles.mapPlaceholderText}>
            La carte interactive avec géolocalisation des besoins et offres d'aide sera bientôt disponible !
          </Text>
          
          {currentLocation && (
            <View style={styles.coordinatesContainer}>
              <Text style={styles.coordinatesText}>
                📍 Lat: {currentLocation.latitude.toFixed(4)}
              </Text>
              <Text style={styles.coordinatesText}>
                📍 Lon: {currentLocation.longitude.toFixed(4)}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Filtres */}
      <View style={styles.filtersContainer}>
        <Text style={styles.filtersTitle}>Filtres</Text>
        <View style={styles.filtersRow}>
          {[
            { key: 'all', label: 'Tout', icon: 'all-inclusive' },
            { key: 'needs', label: 'Besoins', icon: 'help' },
            { key: 'offers', label: 'Offres', icon: 'volunteer-activism' },
            { key: 'urgent', label: 'Urgent', icon: 'priority-high' },
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={styles.filterButton}
              onPress={() => handleFilterToggle(filter.key)}
            >
              <Icon name={filter.icon} size={20} color={Colors.primary} />
              <Text style={styles.filterText}>{filter.label}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Légende */}
      <View style={styles.legendContainer}>
        <Text style={styles.legendTitle}>Légende</Text>
        <View style={styles.legendRow}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: Colors.error }]} />
            <Text style={styles.legendText}>Besoins urgents</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: Colors.primary }]} />
            <Text style={styles.legendText}>Besoins normaux</Text>
          </View>
        </View>
        <View style={styles.legendRow}>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: Colors.success }]} />
            <Text style={styles.legendText}>Offres d'aide</Text>
          </View>
          <View style={styles.legendItem}>
            <View style={[styles.legendDot, { backgroundColor: Colors.accent }]} />
            <Text style={styles.legendText}>Votre position</Text>
          </View>
        </View>
      </View>

      {/* Bouton d'action flottant */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => Alert.alert('Action', 'Ajouter un besoin/offre')}
      >
        <Icon name="add" size={24} color={Colors.background} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.textSecondary,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: Colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: Colors.surface,
  },
  locationInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  locationText: {
    marginLeft: 8,
    fontSize: 16,
    color: Colors.text,
    flex: 1,
  },
  refreshButton: {
    padding: 8,
  },
  mapContainer: {
    flex: 1,
    margin: 16,
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.surface,
  },
  mapPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  mapPlaceholderTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.text,
    marginTop: 16,
    marginBottom: 8,
  },
  mapPlaceholderText: {
    fontSize: 14,
    color: Colors.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
  coordinatesContainer: {
    marginTop: 16,
    padding: 12,
    backgroundColor: Colors.background,
    borderRadius: 8,
  },
  coordinatesText: {
    fontSize: 12,
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  filtersContainer: {
    padding: 16,
    backgroundColor: Colors.surface,
  },
  filtersTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  filtersRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  filterButton: {
    alignItems: 'center',
    padding: 8,
    borderRadius: 8,
    backgroundColor: Colors.background,
    minWidth: 60,
  },
  filterText: {
    fontSize: 12,
    color: Colors.primary,
    marginTop: 4,
  },
  legendContainer: {
    padding: 16,
    backgroundColor: Colors.background,
  },
  legendTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.text,
    marginBottom: 12,
  },
  legendRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 8,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  legendDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginRight: 8,
  },
  legendText: {
    fontSize: 12,
    color: Colors.textSecondary,
  },
  fab: {
    position: 'absolute',
    bottom: 20,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
});

export default MapScreen;
