#!/usr/bin/env node

/**
 * Script de déploiement automatique Nowee
 * Gère le déploiement sur Heroku, Vercel et les stores mobiles
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, cwd = process.cwd()) {
  try {
    log(`Exécution: ${command}`, 'cyan');
    execSync(command, { stdio: 'inherit', cwd });
    return true;
  } catch (error) {
    log(`❌ Erreur: ${error.message}`, 'red');
    return false;
  }
}

function checkPrerequisites() {
  log('🔍 Vérification des prérequis...', 'blue');
  
  const commands = [
    { cmd: 'git --version', name: 'Git' },
    { cmd: 'node --version', name: 'Node.js' },
    { cmd: 'npm --version', name: 'npm' }
  ];

  for (const { cmd, name } of commands) {
    try {
      execSync(cmd, { stdio: 'pipe' });
      log(`✅ ${name} installé`, 'green');
    } catch (error) {
      log(`❌ ${name} non installé`, 'red');
      return false;
    }
  }

  return true;
}

function checkEnvironment() {
  log('🔧 Vérification de l\'environnement...', 'blue');
  
  const envFile = path.join('backend', '.env');
  if (!fs.existsSync(envFile)) {
    log('❌ Fichier .env manquant dans backend/', 'red');
    log('💡 Copiez .env.example vers .env et configurez vos clés', 'yellow');
    return false;
  }

  const envContent = fs.readFileSync(envFile, 'utf8');
  const requiredVars = [
    'SUPABASE_URL',
    'SUPABASE_ANON_KEY',
    'OPENAI_API_KEY',
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN'
  ];

  for (const varName of requiredVars) {
    if (!envContent.includes(`${varName}=`) || envContent.includes(`${varName}=your-`)) {
      log(`❌ Variable ${varName} non configurée`, 'red');
      return false;
    }
  }

  log('✅ Environnement configuré', 'green');
  return true;
}

function deployBackend() {
  log('🚀 Déploiement du backend...', 'blue');
  
  // Vérifier si Heroku CLI est installé
  try {
    execSync('heroku --version', { stdio: 'pipe' });
  } catch (error) {
    log('❌ Heroku CLI non installé', 'red');
    log('💡 Installez depuis: https://devcenter.heroku.com/articles/heroku-cli', 'yellow');
    return false;
  }

  const backendDir = path.join(process.cwd(), 'backend');
  
  // Créer l'application Heroku si elle n'existe pas
  try {
    execSync('heroku apps:info nowee-api-prod', { stdio: 'pipe' });
    log('✅ Application Heroku existe déjà', 'green');
  } catch (error) {
    log('📦 Création de l\'application Heroku...', 'yellow');
    if (!execCommand('heroku create nowee-api-prod', backendDir)) {
      return false;
    }
  }

  // Configurer les variables d'environnement
  log('⚙️ Configuration des variables d\'environnement...', 'yellow');
  const envFile = path.join(backendDir, '.env');
  const envContent = fs.readFileSync(envFile, 'utf8');
  
  const envVars = envContent
    .split('\n')
    .filter(line => line.includes('=') && !line.startsWith('#'))
    .map(line => {
      const [key, ...valueParts] = line.split('=');
      return `${key}=${valueParts.join('=')}`;
    });

  for (const envVar of envVars) {
    execCommand(`heroku config:set ${envVar}`, backendDir);
  }

  // Déployer
  log('🚀 Déploiement sur Heroku...', 'yellow');
  if (!execCommand('git add .', backendDir)) return false;
  if (!execCommand('git commit -m "Deploy to production" || true', backendDir)) return false;
  if (!execCommand('git push heroku main', backendDir)) return false;

  log('✅ Backend déployé avec succès!', 'green');
  return true;
}

function deployFrontend() {
  log('🌐 Déploiement du frontend...', 'blue');
  
  const frontendDir = path.join(process.cwd(), 'frontend');
  
  if (!fs.existsSync(frontendDir)) {
    log('⚠️ Dossier frontend non trouvé, ignoré', 'yellow');
    return true;
  }

  // Vérifier si Vercel CLI est installé
  try {
    execSync('vercel --version', { stdio: 'pipe' });
  } catch (error) {
    log('❌ Vercel CLI non installé', 'red');
    log('💡 Installez avec: npm install -g vercel', 'yellow');
    return false;
  }

  if (!execCommand('vercel --prod', frontendDir)) {
    return false;
  }

  log('✅ Frontend déployé avec succès!', 'green');
  return true;
}

function buildMobile() {
  log('📱 Build de l\'application mobile...', 'blue');
  
  const mobileDir = path.join(process.cwd(), 'mobile');
  
  if (!fs.existsSync(mobileDir)) {
    log('⚠️ Dossier mobile non trouvé, ignoré', 'yellow');
    return true;
  }

  // Installer les dépendances
  if (!execCommand('npm install', mobileDir)) {
    return false;
  }

  // Build Android
  log('🤖 Build Android...', 'yellow');
  if (!execCommand('npm run build:android || true', mobileDir)) {
    log('⚠️ Build Android échoué, continuons...', 'yellow');
  }

  // Build iOS (si sur macOS)
  if (process.platform === 'darwin') {
    log('🍎 Build iOS...', 'yellow');
    if (!execCommand('npm run build:ios || true', mobileDir)) {
      log('⚠️ Build iOS échoué, continuons...', 'yellow');
    }
  }

  log('✅ Build mobile terminé!', 'green');
  return true;
}

function testDeployment() {
  log('🧪 Test du déploiement...', 'blue');
  
  try {
    const response = execSync('curl -s https://nowee-api-prod.herokuapp.com/health', { encoding: 'utf8' });
    const health = JSON.parse(response);
    
    if (health.status === 'OK') {
      log('✅ API en ligne et fonctionnelle!', 'green');
      log(`📊 Version: ${health.version}`, 'cyan');
      return true;
    }
  } catch (error) {
    log('❌ Test de l\'API échoué', 'red');
    return false;
  }
  
  return false;
}

function main() {
  log('🚀 Déploiement Nowee Production', 'bright');
  log('================================', 'bright');

  if (!checkPrerequisites()) {
    process.exit(1);
  }

  if (!checkEnvironment()) {
    process.exit(1);
  }

  // Déploiement backend
  if (!deployBackend()) {
    log('❌ Échec du déploiement backend', 'red');
    process.exit(1);
  }

  // Déploiement frontend
  if (!deployFrontend()) {
    log('❌ Échec du déploiement frontend', 'red');
    process.exit(1);
  }

  // Build mobile
  if (!buildMobile()) {
    log('❌ Échec du build mobile', 'red');
    process.exit(1);
  }

  // Test final
  setTimeout(() => {
    if (testDeployment()) {
      log('🎉 Déploiement réussi!', 'green');
      log('🌐 API: https://nowee-api-prod.herokuapp.com', 'cyan');
      log('📱 Testez via WhatsApp!', 'cyan');
    } else {
      log('⚠️ Déploiement terminé mais tests échoués', 'yellow');
    }
  }, 10000); // Attendre 10s pour que Heroku démarre
}

// Exécution
if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}
