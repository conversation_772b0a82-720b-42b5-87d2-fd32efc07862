{"name": "nowee", "version": "2.0.0", "description": "Nowee - Assistant d'entraide locale en temps réel", "main": "backend/server.js", "type": "module", "scripts": {"start": "cd backend && npm start", "dev": "cd backend && npm run dev", "build": "cd backend && npm run build", "test": "cd backend && npm test", "install-all": "npm install && cd backend && npm install && cd ../mobile && npm install", "setup": "npm run install-all && npm run setup-env", "setup-env": "cp backend/.env.example backend/.env", "deploy": "node deploy.js", "mobile": "cd mobile && npm start", "mobile-android": "cd mobile && npm run android", "mobile-ios": "cd mobile && npm run ios", "lint": "cd backend && npm run lint", "format": "cd backend && npm run format"}, "keywords": ["nowee", "entraide", "whatsapp", "bot", "ai", "community", "senegal", "africa", "local", "assistance"], "author": "Nowee Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/nowee.git"}, "bugs": {"url": "https://github.com/your-username/nowee/issues"}, "homepage": "https://nowee.app", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["backend", "mobile"], "devDependencies": {"concurrently": "^8.2.2", "cross-env": "^7.0.3"}, "dependencies": {"@prisma/client": "^5.7.1", "@supabase/supabase-js": "^2.51.0", "axios": "^1.10.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "openai": "^4.24.1", "twilio": "^4.19.0", "uuid": "^9.0.1"}}