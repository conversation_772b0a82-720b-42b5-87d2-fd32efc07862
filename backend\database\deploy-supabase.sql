-- Nowee Database Schema for Supabase
-- ===================================
-- Script de création des tables pour Nowee
-- À exécuter dans l'éditeur SQL de Supabase

-- Extension pour UUID
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table des utilisateurs
CREATE TABLE IF NOT EXISTS users (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100),
    reputation DECIMAL(3,2) DEFAULT 5.0,
    helps_given INTEGER DEFAULT 0,
    helps_received INTEGER DEFAULT 0,
    location VARCHAR(100),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des portefeuilles (NoweeCoins)
CREATE TABLE IF NOT EXISTS wallets (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    phone VARCHAR(20) UNIQUE NOT NULL,
    coins INTEGER DEFAULT 100,
    total_earned INTEGER DEFAULT 100,
    total_spent INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des besoins
CREATE TABLE IF NOT EXISTS needs (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_phone VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50),
    urgency VARCHAR(20) DEFAULT 'normal',
    location VARCHAR(100),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    status VARCHAR(20) DEFAULT 'active',
    coins_offered INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des offres d'aide
CREATE TABLE IF NOT EXISTS offers (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_phone VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    category VARCHAR(50),
    location VARCHAR(100),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    status VARCHAR(20) DEFAULT 'active',
    coins_requested INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des trocs
CREATE TABLE IF NOT EXISTS barters (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_phone VARCHAR(20) NOT NULL,
    offer_description TEXT NOT NULL,
    need_description TEXT NOT NULL,
    category VARCHAR(50),
    location VARCHAR(100),
    latitude DECIMAL(10,8),
    longitude DECIMAL(11,8),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des correspondances (matching)
CREATE TABLE IF NOT EXISTS matches (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    need_id UUID REFERENCES needs(id),
    offer_id UUID REFERENCES offers(id),
    requester_phone VARCHAR(20) NOT NULL,
    helper_phone VARCHAR(20) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    relevance_score DECIMAL(3,2),
    distance_km DECIMAL(6,2),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des transactions
CREATE TABLE IF NOT EXISTS transactions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    from_phone VARCHAR(20) NOT NULL,
    to_phone VARCHAR(20) NOT NULL,
    amount INTEGER NOT NULL,
    reason TEXT,
    transaction_type VARCHAR(20) DEFAULT 'transfer',
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des conversations
CREATE TABLE IF NOT EXISTS conversations (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_phone VARCHAR(20) NOT NULL,
    message TEXT NOT NULL,
    response TEXT,
    ai_model VARCHAR(50) DEFAULT 'gpt-4o-mini',
    processing_time_ms INTEGER,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des statistiques économiques
CREATE TABLE IF NOT EXISTS economy_stats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    total_coins INTEGER DEFAULT 0,
    active_users INTEGER DEFAULT 0,
    transactions_today INTEGER DEFAULT 0,
    average_transaction DECIMAL(8,2) DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Table des statistiques communauté
CREATE TABLE IF NOT EXISTS community_stats (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    total_helps INTEGER DEFAULT 0,
    active_helpers INTEGER DEFAULT 0,
    success_rate INTEGER DEFAULT 0,
    average_response_time INTEGER DEFAULT 0,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fonction pour transférer des coins
CREATE OR REPLACE FUNCTION transfer_coins(
    from_phone VARCHAR(20),
    to_phone VARCHAR(20),
    amount INTEGER,
    reason TEXT DEFAULT 'Transfer'
)
RETURNS BOOLEAN AS $$
DECLARE
    from_balance INTEGER;
BEGIN
    -- Vérifier le solde
    SELECT coins INTO from_balance FROM wallets WHERE phone = from_phone;
    
    IF from_balance < amount THEN
        RETURN FALSE;
    END IF;
    
    -- Débiter l'expéditeur
    UPDATE wallets 
    SET coins = coins - amount, 
        total_spent = total_spent + amount,
        updated_at = NOW()
    WHERE phone = from_phone;
    
    -- Créditer le destinataire
    INSERT INTO wallets (phone, coins, total_earned)
    VALUES (to_phone, amount, amount)
    ON CONFLICT (phone) 
    DO UPDATE SET 
        coins = wallets.coins + amount,
        total_earned = wallets.total_earned + amount,
        updated_at = NOW();
    
    -- Enregistrer la transaction
    INSERT INTO transactions (from_phone, to_phone, amount, reason)
    VALUES (from_phone, to_phone, amount, reason);
    
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Fonction pour mettre à jour les statistiques
CREATE OR REPLACE FUNCTION update_economy_stats()
RETURNS VOID AS $$
BEGIN
    INSERT INTO economy_stats (
        total_coins,
        active_users,
        transactions_today,
        average_transaction
    )
    SELECT 
        COALESCE(SUM(coins), 0) as total_coins,
        COUNT(DISTINCT phone) as active_users,
        (SELECT COUNT(*) FROM transactions WHERE DATE(created_at) = CURRENT_DATE) as transactions_today,
        (SELECT COALESCE(AVG(amount), 0) FROM transactions WHERE DATE(created_at) = CURRENT_DATE) as average_transaction
    FROM wallets
    ON CONFLICT (id) DO UPDATE SET
        total_coins = EXCLUDED.total_coins,
        active_users = EXCLUDED.active_users,
        transactions_today = EXCLUDED.transactions_today,
        average_transaction = EXCLUDED.average_transaction,
        updated_at = NOW();
END;
$$ LANGUAGE plpgsql;

-- Triggers pour mettre à jour updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Appliquer les triggers
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_wallets_updated_at BEFORE UPDATE ON wallets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_needs_updated_at BEFORE UPDATE ON needs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_offers_updated_at BEFORE UPDATE ON offers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_barters_updated_at BEFORE UPDATE ON barters FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_matches_updated_at BEFORE UPDATE ON matches FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Index pour les performances
CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
CREATE INDEX IF NOT EXISTS idx_wallets_phone ON wallets(phone);
CREATE INDEX IF NOT EXISTS idx_needs_user_phone ON needs(user_phone);
CREATE INDEX IF NOT EXISTS idx_needs_status ON needs(status);
CREATE INDEX IF NOT EXISTS idx_offers_user_phone ON offers(user_phone);
CREATE INDEX IF NOT EXISTS idx_offers_status ON offers(status);
CREATE INDEX IF NOT EXISTS idx_barters_user_phone ON barters(user_phone);
CREATE INDEX IF NOT EXISTS idx_transactions_from_phone ON transactions(from_phone);
CREATE INDEX IF NOT EXISTS idx_transactions_to_phone ON transactions(to_phone);
CREATE INDEX IF NOT EXISTS idx_conversations_user_phone ON conversations(user_phone);

-- Données initiales
INSERT INTO economy_stats (total_coins, active_users, transactions_today, average_transaction)
VALUES (10000, 150, 25, 15.50)
ON CONFLICT DO NOTHING;

INSERT INTO community_stats (total_helps, active_helpers, success_rate, average_response_time)
VALUES (500, 75, 85, 12)
ON CONFLICT DO NOTHING;

-- Politique de sécurité RLS (Row Level Security)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE wallets ENABLE ROW LEVEL SECURITY;
ALTER TABLE needs ENABLE ROW LEVEL SECURITY;
ALTER TABLE offers ENABLE ROW LEVEL SECURITY;
ALTER TABLE barters ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;

-- Politiques d'accès (permettre lecture/écriture pour l'API)
CREATE POLICY "Allow all operations" ON users FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON wallets FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON needs FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON offers FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON barters FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON transactions FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON conversations FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON economy_stats FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON community_stats FOR ALL USING (true);
CREATE POLICY "Allow all operations" ON matches FOR ALL USING (true);
