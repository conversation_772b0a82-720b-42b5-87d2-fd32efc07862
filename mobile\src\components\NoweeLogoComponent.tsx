/**
 * Composant Logo Nowee
 * Logo vectoriel intégré avec animations
 */

import React from 'react';
import { View, Text, StyleSheet, Animated } from 'react-native';
import { Colors } from '../utils/Colors';

interface NoweeLogoProps {
  size?: 'small' | 'medium' | 'large';
  animated?: boolean;
  showText?: boolean;
  color?: string;
}

const NoweeLogoComponent: React.FC<NoweeLogoProps> = ({
  size = 'medium',
  animated = false,
  showText = true,
  color = Colors.primary,
}) => {
  const animatedValue = new Animated.Value(0);

  React.useEffect(() => {
    if (animated) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(animatedValue, {
            toValue: 1,
            duration: 2000,
            useNativeDriver: true,
          }),
          Animated.timing(animatedValue, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    }
  }, [animated]);

  const getSizes = () => {
    switch (size) {
      case 'small':
        return { logo: 32, text: 16, container: 40 };
      case 'large':
        return { logo: 80, text: 24, container: 100 };
      default:
        return { logo: 48, text: 20, container: 60 };
    }
  };

  const sizes = getSizes();

  const animatedStyle = animated
    ? {
        transform: [
          {
            scale: animatedValue.interpolate({
              inputRange: [0, 1],
              outputRange: [1, 1.1],
            }),
          },
          {
            rotate: animatedValue.interpolate({
              inputRange: [0, 1],
              outputRange: ['0deg', '5deg'],
            }),
          },
        ],
      }
    : {};

  return (
    <View style={[styles.container, { height: sizes.container }]}>
      <Animated.View style={[styles.logoContainer, animatedStyle]}>
        {/* Logo SVG-like avec émojis et formes */}
        <View style={[styles.logoCircle, { 
          width: sizes.logo, 
          height: sizes.logo,
          backgroundColor: color,
        }]}>
          <Text style={[styles.logoEmoji, { fontSize: sizes.logo * 0.6 }]}>
            🤝
          </Text>
        </View>
        
        {/* Effet de rayonnement */}
        {animated && (
          <Animated.View
            style={[
              styles.glowEffect,
              {
                width: sizes.logo * 1.5,
                height: sizes.logo * 1.5,
                opacity: animatedValue.interpolate({
                  inputRange: [0, 1],
                  outputRange: [0.3, 0],
                }),
                transform: [
                  {
                    scale: animatedValue.interpolate({
                      inputRange: [0, 1],
                      outputRange: [1, 1.5],
                    }),
                  },
                ],
              },
            ]}
          />
        )}
      </Animated.View>

      {showText && (
        <Text style={[styles.logoText, { 
          fontSize: sizes.text,
          color: color,
        }]}>
          Nowee
        </Text>
      )}
    </View>
  );
};

// Logo simple pour les cas où on a besoin juste du texte
export const NoweeTextLogo: React.FC<{ size?: number; color?: string }> = ({
  size = 24,
  color = Colors.primary,
}) => (
  <Text style={[styles.textOnlyLogo, { fontSize: size, color }]}>
    🤝 Nowee
  </Text>
);

// Logo minimaliste pour les notifications
export const NoweeIconLogo: React.FC<{ size?: number }> = ({ size = 24 }) => (
  <View style={[styles.iconLogo, { width: size, height: size }]}>
    <Text style={{ fontSize: size * 0.8 }}>🤝</Text>
  </View>
);

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoCircle: {
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  logoEmoji: {
    textAlign: 'center',
  },
  glowEffect: {
    position: 'absolute',
    borderRadius: 50,
    backgroundColor: Colors.primary,
    top: '50%',
    left: '50%',
    marginTop: -37.5, // -width/2
    marginLeft: -37.5, // -height/2
  },
  logoText: {
    fontWeight: 'bold',
    marginTop: 8,
    textAlign: 'center',
  },
  textOnlyLogo: {
    fontWeight: 'bold',
  },
  iconLogo: {
    borderRadius: 12,
    backgroundColor: Colors.surface,
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default NoweeLogoComponent;
