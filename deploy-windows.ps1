# Script de déploiement Nowee pour Windows
Write-Host "🚀 Déploiement Nowee en production" -ForegroundColor Blue

# Vérifier Git
if (!(Get-Command git -ErrorAction SilentlyContinue)) {
    Write-Host "❌ Git non installé" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Git disponible" -ForegroundColor Green

# Instructions pour l'utilisateur
Write-Host ""
Write-Host "📋 ÉTAPES DE DÉPLOIEMENT:" -ForegroundColor Cyan
Write-Host "1. Configurer Supabase (voir database/deploy-supabase.sql)" -ForegroundColor Yellow
Write-Host "2. Installer Heroku CLI: https://devcenter.heroku.com/articles/heroku-cli" -ForegroundColor Yellow
Write-Host "3. Installer Vercel CLI: npm install -g vercel" -ForegroundColor Yellow
Write-Host "4. Exécuter: heroku create nowee-api-prod" -ForegroundColor Yellow
Write-Host "5. Configurer les variables d'environnement" -ForegroundColor Yellow
Write-Host "6. Déployer: git push heroku main" -ForegroundColor Yellow
Write-Host ""
Write-Host "🎯 Configuration terminée ! Suivez les étapes ci-dessus." -ForegroundColor Green
