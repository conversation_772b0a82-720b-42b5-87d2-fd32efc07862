/**
 * Nowee Mobile App
 * Application React Native pour l'entraide locale
 */

import React, { useEffect } from 'react';
import {
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  View,
  Alert,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import Icon from 'react-native-vector-icons/MaterialIcons';

// Import des écrans
import HomeScreen from './src/screens/HomeScreen';
import ChatScreen from './src/screens/ChatScreen';
import MapScreen from './src/screens/MapScreen';
import WalletScreen from './src/screens/WalletScreen';
import ProfileScreen from './src/screens/ProfileScreen';

// Services
import { initializeServices } from './src/services/AppService';

const Tab = createBottomTabNavigator();

// Couleurs Nowee
const Colors = {
  primary: '#2E7D32',
  secondary: '#4CAF50',
  accent: '#FF9800',
  background: '#FFFFFF',
  surface: '#F5F5F5',
  text: '#212121',
  textSecondary: '#757575',
  error: '#F44336',
  success: '#4CAF50',
  warning: '#FF9800',
};

function App(): JSX.Element {
  useEffect(() => {
    // Initialisation des services
    initializeServices()
      .then(() => {
        console.log('✅ Services Nowee initialisés');
      })
      .catch((error) => {
        console.error('❌ Erreur initialisation:', error);
        Alert.alert(
          'Erreur',
          'Impossible d\'initialiser l\'application. Vérifiez votre connexion.',
        );
      });
  }, []);

  return (
    <NavigationContainer>
      <StatusBar
        barStyle="light-content"
        backgroundColor={Colors.primary}
      />
      <Tab.Navigator
        screenOptions={({ route }) => ({
          tabBarIcon: ({ focused, color, size }) => {
            let iconName: string;

            switch (route.name) {
              case 'Accueil':
                iconName = 'home';
                break;
              case 'Chat':
                iconName = 'chat';
                break;
              case 'Carte':
                iconName = 'map';
                break;
              case 'Portefeuille':
                iconName = 'account-balance-wallet';
                break;
              case 'Profil':
                iconName = 'person';
                break;
              default:
                iconName = 'help';
            }

            return <Icon name={iconName} size={size} color={color} />;
          },
          tabBarActiveTintColor: Colors.primary,
          tabBarInactiveTintColor: Colors.textSecondary,
          tabBarStyle: {
            backgroundColor: Colors.background,
            borderTopColor: Colors.surface,
            height: 60,
            paddingBottom: 8,
            paddingTop: 8,
          },
          headerStyle: {
            backgroundColor: Colors.primary,
          },
          headerTintColor: Colors.background,
          headerTitleStyle: {
            fontWeight: 'bold',
          },
        })}
      >
        <Tab.Screen
          name="Accueil"
          component={HomeScreen}
          options={{
            title: '🏠 Accueil',
          }}
        />
        <Tab.Screen
          name="Chat"
          component={ChatScreen}
          options={{
            title: '💬 Chat IA',
          }}
        />
        <Tab.Screen
          name="Carte"
          component={MapScreen}
          options={{
            title: '🗺️ Carte',
          }}
        />
        <Tab.Screen
          name="Portefeuille"
          component={WalletScreen}
          options={{
            title: '💰 Portefeuille',
          }}
        />
        <Tab.Screen
          name="Profil"
          component={ProfileScreen}
          options={{
            title: '👤 Profil',
          }}
        />
      </Tab.Navigator>
    </NavigationContainer>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
});

export default App;
