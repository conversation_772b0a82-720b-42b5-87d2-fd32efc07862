# Render.com - Configuration de déploiement Nowee
# ==============================================
# Déploiement automatique sur Render.com

services:
  # API Backend Nowee
  - type: web
    name: nowee-api
    env: node
    plan: starter
    buildCommand: cd backend && npm install
    startCommand: cd backend && npm start
    envVars:
      - key: NODE_ENV
        value: production
      - key: PORT
        value: 10000
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_ANON_KEY
        sync: false
      - key: SUPABASE_SERVICE_KEY
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: TWILIO_ACCOUNT_SID
        sync: false
      - key: TWILIO_AUTH_TOKEN
        sync: false
      - key: TWILIO_WHATSAPP_NUMBER
        sync: false
      - key: JWT_SECRET
        generateValue: true
    healthCheckPath: /health
    autoDeploy: true
    
  # Frontend Dashboard Admin (optionnel)
  - type: web
    name: nowee-admin
    env: static
    buildCommand: cd admin && npm install && npm run build
    staticPublishPath: admin/dist
    envVars:
      - key: REACT_APP_API_URL
        fromService:
          type: web
          name: nowee-api
          property: host
    autoDeploy: true

# Base de données (utilise Supabase externe)
# Pas besoin de configurer ici car Supabase est externe
