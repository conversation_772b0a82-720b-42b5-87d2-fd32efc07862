{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "cd backend && npm install", "watchPatterns": ["backend/**"]}, "deploy": {"startCommand": "cd backend && npm start", "healthcheckPath": "/health", "healthcheckTimeout": 100, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10}, "environments": {"production": {"variables": {"NODE_ENV": "production", "PORT": "${{RAILWAY_PORT}}", "RAILWAY_STATIC_URL": "${{RAILWAY_STATIC_URL}}", "DATABASE_URL": "${{DATABASE_URL}}"}}}}