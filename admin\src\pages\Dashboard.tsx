import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Users,
  HelpCircle,
  Coins,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  Activity,
} from 'lucide-react';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
} from 'recharts';

// Données simulées (à remplacer par des appels API réels)
const mockStats = {
  totalUsers: 1247,
  activeUsers: 892,
  totalNeeds: 3456,
  resolvedNeeds: 2891,
  totalCoins: 125000,
  transactionsToday: 89,
  averageResponseTime: 12, // minutes
  successRate: 84, // pourcentage
};

const mockChartData = [
  { name: 'Lun', users: 120, needs: 45, resolved: 38 },
  { name: 'Mar', users: 132, needs: 52, resolved: 44 },
  { name: 'Mer', users: 101, needs: 38, resolved: 32 },
  { name: 'Jeu', users: 134, needs: 48, resolved: 41 },
  { name: 'Ven', users: 165, needs: 62, resolved: 55 },
  { name: '<PERSON>', users: 142, needs: 55, resolved: 48 },
  { name: 'Di<PERSON>', users: 98, needs: 35, resolved: 29 },
];

const mockCategoryData = [
  { name: 'Transport', value: 35, color: '#8884d8' },
  { name: 'Courses', value: 28, color: '#82ca9d' },
  { name: 'Bricolage', value: 20, color: '#ffc658' },
  { name: 'Garde enfants', value: 12, color: '#ff7300' },
  { name: 'Autres', value: 5, color: '#00ff00' },
];

const Dashboard: React.FC = () => {
  const { data: stats, isLoading } = useQuery({
    queryKey: ['dashboard-stats'],
    queryFn: async () => {
      // Simuler un appel API
      await new Promise(resolve => setTimeout(resolve, 1000));
      return mockStats;
    },
  });

  const StatCard: React.FC<{
    title: string;
    value: string | number;
    icon: React.ElementType;
    change?: string;
    changeType?: 'positive' | 'negative' | 'neutral';
  }> = ({ title, value, icon: Icon, change, changeType = 'neutral' }) => (
    <div className="bg-white overflow-hidden shadow rounded-lg">
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <Icon className="h-6 w-6 text-gray-400" />
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 truncate">{title}</dt>
              <dd className="flex items-baseline">
                <div className="text-2xl font-semibold text-gray-900">{value}</div>
                {change && (
                  <div
                    className={`ml-2 flex items-baseline text-sm font-semibold ${
                      changeType === 'positive'
                        ? 'text-green-600'
                        : changeType === 'negative'
                        ? 'text-red-600'
                        : 'text-gray-600'
                    }`}
                  >
                    {change}
                  </div>
                )}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-green-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* En-tête */}
      <div>
        <h1 className="text-2xl font-semibold text-gray-900">Tableau de bord</h1>
        <p className="mt-2 text-sm text-gray-700">
          Vue d'ensemble de l'activité Nowee en temps réel
        </p>
      </div>

      {/* Statistiques principales */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Utilisateurs totaux"
          value={stats?.totalUsers.toLocaleString() || '0'}
          icon={Users}
          change="+12%"
          changeType="positive"
        />
        <StatCard
          title="Utilisateurs actifs"
          value={stats?.activeUsers.toLocaleString() || '0'}
          icon={Activity}
          change="+8%"
          changeType="positive"
        />
        <StatCard
          title="Besoins résolus"
          value={`${stats?.resolvedNeeds || 0}/${stats?.totalNeeds || 0}`}
          icon={CheckCircle}
          change={`${stats ? Math.round((stats.resolvedNeeds / stats.totalNeeds) * 100) : 0}%`}
          changeType="positive"
        />
        <StatCard
          title="NoweeCoins en circulation"
          value={stats?.totalCoins.toLocaleString() || '0'}
          icon={Coins}
          change="+15%"
          changeType="positive"
        />
      </div>

      {/* Métriques de performance */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-3">
        <StatCard
          title="Temps de réponse moyen"
          value={`${stats?.averageResponseTime || 0} min`}
          icon={Clock}
          change="-2 min"
          changeType="positive"
        />
        <StatCard
          title="Taux de succès"
          value={`${stats?.successRate || 0}%`}
          icon={TrendingUp}
          change="+3%"
          changeType="positive"
        />
        <StatCard
          title="Transactions aujourd'hui"
          value={stats?.transactionsToday || 0}
          icon={AlertCircle}
          change="+23"
          changeType="positive"
        />
      </div>

      {/* Graphiques */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Activité hebdomadaire */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Activité de la semaine
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={mockChartData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="users"
                stroke="#8884d8"
                strokeWidth={2}
                name="Utilisateurs actifs"
              />
              <Line
                type="monotone"
                dataKey="needs"
                stroke="#82ca9d"
                strokeWidth={2}
                name="Nouveaux besoins"
              />
              <Line
                type="monotone"
                dataKey="resolved"
                stroke="#ffc658"
                strokeWidth={2}
                name="Besoins résolus"
              />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Répartition par catégorie */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Besoins par catégorie
          </h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={mockCategoryData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {mockCategoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Activité récente */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Activité récente
          </h3>
          <div className="flow-root">
            <ul className="-mb-8">
              {[
                {
                  id: 1,
                  content: 'Nouveau besoin créé par Marie D.',
                  target: 'Aide pour déménagement à Dakar',
                  time: 'Il y a 2 minutes',
                  icon: HelpCircle,
                  iconBackground: 'bg-blue-500',
                },
                {
                  id: 2,
                  content: 'Besoin résolu par Ahmed S.',
                  target: 'Réparation de vélo',
                  time: 'Il y a 5 minutes',
                  icon: CheckCircle,
                  iconBackground: 'bg-green-500',
                },
                {
                  id: 3,
                  content: 'Transfert de 15 NoweeCoins',
                  target: 'De Fatou vers Mamadou',
                  time: 'Il y a 8 minutes',
                  icon: Coins,
                  iconBackground: 'bg-yellow-500',
                },
                {
                  id: 4,
                  content: 'Nouvel utilisateur inscrit',
                  target: 'Ibrahima K. a rejoint Nowee',
                  time: 'Il y a 12 minutes',
                  icon: Users,
                  iconBackground: 'bg-purple-500',
                },
              ].map((item, itemIdx) => (
                <li key={item.id}>
                  <div className="relative pb-8">
                    {itemIdx !== 3 ? (
                      <span
                        className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                        aria-hidden="true"
                      />
                    ) : null}
                    <div className="relative flex space-x-3">
                      <div>
                        <span
                          className={`${item.iconBackground} h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white`}
                        >
                          <item.icon className="h-4 w-4 text-white" />
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-500">
                            {item.content}{' '}
                            <span className="font-medium text-gray-900">
                              {item.target}
                            </span>
                          </p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500">
                          {item.time}
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
