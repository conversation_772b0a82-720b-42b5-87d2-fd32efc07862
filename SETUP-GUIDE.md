# 🚀 Guide de Configuration Nowee

Guide complet pour configurer et démarrer le projet Nowee localement.

## 📋 Prérequis

### Outils requis
- **Node.js** >= 18.0.0
- **npm** >= 9.0.0
- **Git**

### Pour le développement mobile
- **React Native CLI** : `npm install -g react-native-cli`
- **Android Studio** (pour Android)
- **Xcode** (pour iOS, macOS uniquement)

### Comptes de service requis
- **Supabase** : https://supabase.com (base de données)
- **OpenAI** : https://openai.com (IA)
- **Twilio** : https://twilio.com (WhatsApp)

## 🛠️ Installation

### 1. C<PERSON><PERSON> le projet
```bash
git clone https://github.com/your-username/nowee.git
cd nowee
```

### 2. Installation automatique
```bash
npm run setup
```

Cette commande va :
- Installer les dépendances du projet principal
- Installer les dépendances du backend
- Installer les dépendances de l'app mobile
- Créer le fichier `.env` depuis l'exemple

### 3. Configuration des variables d'environnement

Éditez le fichier `backend/.env` avec vos clés :

```env
# Base de données Supabase
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_KEY=your-service-key

# OpenAI
OPENAI_API_KEY=sk-your-openai-api-key

# Twilio WhatsApp
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_NUMBER=whatsapp:+***********

# JWT Secret
JWT_SECRET=your-super-secret-jwt-key
```

### 4. Configuration de la base de données

1. Créez un compte sur [Supabase](https://supabase.com)
2. Créez un nouveau projet "nowee-dev"
3. Allez dans **SQL Editor**
4. Copiez et exécutez le contenu de `backend/database/deploy-supabase.sql`
5. Notez l'URL et les clés de votre projet

## 🚀 Démarrage

### Backend (API + WhatsApp Bot)
```bash
# Démarrage en mode développement
npm run dev

# Ou démarrage normal
npm start
```

L'API sera accessible sur : http://localhost:3000

### Application Mobile

#### Prérequis mobile
```bash
# Installer les dépendances globales
npm install -g react-native-cli

# Pour Android, installer Android Studio
# Pour iOS, installer Xcode (macOS uniquement)
```

#### Démarrage de l'app mobile
```bash
# Démarrer Metro bundler
npm run mobile

# Dans un autre terminal, lancer l'app
npm run mobile-android  # Pour Android
npm run mobile-ios      # Pour iOS
```

## 🧪 Tests

### Tester l'API
```bash
# Health check
curl http://localhost:3000/health

# Test du webhook WhatsApp (simulé)
curl -X POST http://localhost:3000/webhook \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "Body=Bonjour&From=whatsapp:+221771234567"
```

### Tester l'application mobile
1. Ouvrez l'émulateur Android ou iOS
2. L'application se lance automatiquement
3. Testez les différents écrans et fonctionnalités

## 🔧 Configuration avancée

### WhatsApp avec Twilio

1. Créez un compte [Twilio](https://twilio.com)
2. Activez le **WhatsApp Business API**
3. Configurez le webhook : `https://your-domain.com/webhook`
4. Testez avec le sandbox Twilio

### Géolocalisation

L'app mobile demande automatiquement les permissions de géolocalisation.

Pour Android, ajoutez dans `android/app/src/main/AndroidManifest.xml` :
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
```

### Notifications Push

Configuration automatique via `react-native-push-notification`.

## 📱 Build de production

### Backend
```bash
cd backend
npm run build
```

### Mobile Android
```bash
cd mobile
npm run build:android
```

### Mobile iOS
```bash
cd mobile
npm run build:ios
```

## 🚀 Déploiement

### Déploiement automatique
```bash
npm run deploy
```

### Déploiement manuel

#### Backend sur Heroku
```bash
cd backend
heroku create nowee-api-prod
git push heroku main
```

#### Mobile sur les stores
1. **Google Play Store** : Uploadez l'APK généré
2. **Apple App Store** : Uploadez via Xcode

## 🐛 Dépannage

### Problèmes courants

#### "Metro bundler ne démarre pas"
```bash
cd mobile
npm run reset
```

#### "Erreur de permissions Android"
```bash
cd mobile/android
./gradlew clean
cd ..
npm run android
```

#### "API non accessible"
- Vérifiez que le backend est démarré
- Vérifiez les variables d'environnement
- Testez avec `curl http://localhost:3000/health`

#### "Base de données non accessible"
- Vérifiez les clés Supabase
- Vérifiez que le script SQL a été exécuté
- Testez la connexion depuis Supabase

### Logs utiles
```bash
# Logs backend
tail -f backend/nowee.log

# Logs mobile Android
npx react-native log-android

# Logs mobile iOS
npx react-native log-ios
```

## 📞 Support

### Ressources
- **Documentation** : README.md
- **Issues** : GitHub Issues
- **Discord** : [Communauté Nowee](https://discord.gg/nowee)

### Contact
- **Email** : <EMAIL>
- **Support** : <EMAIL>

---

**🎉 Félicitations ! Nowee est maintenant configuré et prêt à révolutionner l'entraide locale ! 🎉**
