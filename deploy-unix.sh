#!/bin/bash
echo "🚀 Déploiement Nowee en production"

# Vérifier les outils
command -v git >/dev/null 2>&1 || { echo "❌ Git requis"; exit 1; }
command -v heroku >/dev/null 2>&1 || { echo "❌ Heroku CLI requis"; exit 1; }
command -v vercel >/dev/null 2>&1 || { echo "❌ Vercel CLI requis"; exit 1; }

echo "✅ Outils disponibles"

# Déploiement automatique
echo "📦 Déploiement backend..."
cd backend
heroku create nowee-api-prod
git push heroku main

echo "🌐 Déploiement frontend..."
cd ../frontend
vercel --prod

echo "✅ Déploiement terminé !"
